generator client {
  provider      = "prisma-client-js"
  output        = ".../../../generated/und"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("UND_DATABASE_URL")
}

model MerchantConfig {
  id           Int       @id @default(autoincrement())
  merchantId   String    @map("merchant_id") @db.VarChar(50)
  merchantName String    @map("merchant_name") @db.VarChar(255)
  storeId      String    @map("store_id") @db.VarChar(50)
  storeName    String    @map("store_name") @db.VarChar(255)
  isActive     Boolean?  @default(true) @map("is_active")
  createdAt    DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy    String?   @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt    DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy    String?   @map("updated_by") @db.VarChar(50)

  merchantConfigRules MerchantConfigRule[]

  @@unique([merchantId, storeId], name: "merchant_id_store_id")
  @@index([id])
  @@index([merchantId])
  @@index([storeId])
  @@map("merchant_configs")
}

model MerchantConfigRule {
  id               Int                    @id @default(autoincrement())
  merchantConfigId Int                    @map("merchant_config_id")
  ruleKey          EMerchantConfigRuleKey @map("rule_key")
  paymentChannel   PaymentChannel         @default(ALL) @map("payment_channel")
  isActive         Boolean                @map("is_active")
  createdAt        DateTime?              @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy        String?                @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt        DateTime?              @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy        String?                @map("updated_by") @db.VarChar(50)

  merchantConfig MerchantConfig @relation(fields: [merchantConfigId], references: [id])

  @@unique([merchantConfigId, ruleKey], name: "merchant_config_id_rule_key")
  @@index([id])
  @@index([merchantConfigId])
  @@index([ruleKey])
  @@index([merchantConfigId, ruleKey], map: "merchant_config_id_and_rule_key")
  @@map("merchant_config_rules")
}

model BlackListAddress {
  id                Int          @id @default(autoincrement())
  address           String       @map("address")
  addressNormalized String       @map("address_normalized") @db.VarChar(255)
  hashAddress       String       @map("hash_address") @db.VarChar(255)
  compareMode       ECompareMode @map("compare_mode")
  isActive          Boolean      @map("is_active")
  createdAt         DateTime?    @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy         String?      @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt         DateTime?    @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy         String?      @map("updated_by") @db.VarChar(50)

  // TODO index here
  @@index([address])
  @@index([addressNormalized])
  @@map("blacklist_address")
}

model BlacklistIdCards {
  id                Int      @default(autoincrement())
  idcardNumber      String   @id(map: "blacklist_idcards_pk") @map("idcard_number") @db.VarChar(15)
  idcardName        String   @map("idcard_name") @db.VarChar(50)
  idcardDescription String?  @map("idcard_description") @db.VarChar(255)
  idcardIsStatus    Int      @default(1) @map("idcard_is_status") @db.SmallInt
  lastModifiedDate  DateTime @default(now()) @map("last_modified_date") @db.Timestamp(6)
  lastModifiedBy    String   @default("SYS") @map("last_modified_by") @db.VarChar(20)

  @@map("blacklist_id_cards")
}

model BlacklistItemMerchants {
  id              Int            @id @default(autoincrement())
  blacklistItemId Int            @map("blim_blacklist_item_id")
  merchantId      String         @map("blim_merchant_id") @db.VarChar(100)
  merchantName    String         @map("blim_merchant_name") @db.VarChar(255)
  storeId         String         @map("blim_store_id") @db.VarChar(100)
  storeName       String         @map("blim_store_name") @db.VarChar(255)
  updatedAt       DateTime?      @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy       String         @map("updated_by") @db.VarChar(50)
  blacklistItems  BlacklistItems @relation(fields: [blacklistItemId], references: [id])

  @@index([merchantId])
  @@index([blacklistItemId], map: "blacklist_item_id")
  @@map("blacklist_item_merchants")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model BlacklistItems {
  id             Int                      @id @default(autoincrement())
  itemId         String                   @map("bli_item_id") @db.VarChar(50)
  itemName       String                   @map("bli_item_name") @db.VarChar(500)
  blockType      String                   @map("bli_block_type") @db.VarChar(30)
  extScore       Decimal?                 @map("bli_ext_score") @db.Decimal
  minQuantity    Decimal                  @map("bli_min_quantity") @db.Decimal
  maxQuantity    Decimal                  @map("bli_max_quantity") @db.Decimal
  maxTotalAmount Decimal                  @map("bli_max_total_amount") @db.Decimal
  fromDate       DateTime                 @map("bli_from_date") @db.Timestamp(6)
  toDate         DateTime                 @map("bli_to_date") @db.Timestamp(6)
  activated      Boolean                  @map("bli_activated")
  createdAt      DateTime                 @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy      String                   @map("created_by") @db.VarChar(50)
  updatedAt      DateTime?                @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy      String?                  @map("updated_by") @db.VarChar(50)
  paymentChannel PaymentChannel           @default(ALL) @map("bli_payment_channel")
  merchants      BlacklistItemMerchants[]

  @@index([itemId])
  @@index([itemId, blockType], map: "item_id_and_block_type")
  @@map("blacklist_items")
}

model BlacklistPhones {
  id               Int      @default(autoincrement())
  phoneNumber      String   @id(map: "blacklist_phones_pk") @map("phone_number") @db.VarChar(15)
  phoneName        String   @map("phone_name") @db.VarChar(50)
  phoneNotes       String?  @map("phone_notes") @db.VarChar(255)
  phoneIsStatus    Int      @default(1) @map("phone_is_status") @db.SmallInt
  lastModifiedDate DateTime @default(now()) @map("last_modified_date") @db.Timestamp(6)
  lastModifiedBy   String   @default("SYS") @map("last_modified_by") @db.VarChar(20)

  @@map("blacklist_phones")
}

model LocationRestrictionApplication {
  id                            Int              @id @default(autoincrement())
  applicationId                 String           @map("application_id") @db.VarChar(100)
  gwRequestId                   String?          @map("gw_request_id") @db.VarChar(100)
  locationRestrictionId         Int?             @map("location_restriction_id")
  locationRestrictionDistanceId Int?             @map("location_restriction_distance_id")
  minDistance                   Decimal?         @map("min_distance") @db.Decimal
  maxDistance                   Decimal?         @map("max_distance") @db.Decimal
  realityDistanceInKm           Decimal?         @map("distance_in_km") @db.Decimal
  calcDistanceBy                ECalcDistanceBy? @map("calc_distance_by")
  originLocation                Json?            @map("origin_location")
  destinationLocation           Json?            @map("destination_location")
  metadata                      Json?            @map("metadata")
  isMatched                     Boolean?         @map("is_matched")
  createdAt                     DateTime?        @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy                     String?          @default("SYS") @map("created_by") @db.VarChar(100)
  updatedAt                     DateTime?        @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy                     String?          @map("updated_by") @db.VarChar(100)

  locationRestriction LocationRestriction?         @relation(fields: [locationRestrictionId], references: [id])
  distance            LocationDistanceRestriction? @relation(fields: [locationRestrictionDistanceId], references: [id])

  @@index([applicationId])
  @@map("location_restriction_application")
}

model LocationRestriction {
  id             Int            @id @default(autoincrement())
  merchantId     String         @map("merchant_id") @db.VarChar(100)
  merchantName   String         @map("merchant_name") @db.VarChar(255)
  storeId        String         @map("store_id") @db.VarChar(100)
  storeName      String         @map("store_name") @db.VarChar(255)
  lat            Decimal?       @map("lat") @db.Decimal
  lng            Decimal?       @map("lng") @db.Decimal
  applyAllTime   Boolean        @map("apply_all_time")
  paymentChannel PaymentChannel @default(ALL) @map("payment_channel")
  isActive       Boolean        @map("is_active")
  createdAt      DateTime?      @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy      String?        @default("SYS") @map("created_by") @db.VarChar(100)
  updatedAt      DateTime?      @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy      String?        @map("updated_by") @db.VarChar(100)

  times        LocationTimeRestriction[]
  distances    LocationDistanceRestriction[]
  applications LocationRestrictionApplication[]

  // TODO: index
  @@unique([merchantId, storeId], name: "merchant_id_store_id")
  @@map("location_restrictions")
}

model LocationTimeRestriction {
  id                    Int       @id @default(autoincrement())
  locationRestrictionId Int       @map("location_restriction_id")
  fromHours             Int?      @map("from_hours")
  fromMinutes           Int?      @map("from_minutes")
  toHours               Int?      @map("to_hours")
  toMinutes             Int?      @map("to_minutes")
  isActive              Boolean   @default(true) @map("is_active")
  createdAt             DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy             String?   @default("SYS") @map("created_by") @db.VarChar(100)
  updatedAt             DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy             String?   @map("updated_by") @db.VarChar(100)

  locationRestriction LocationRestriction? @relation(fields: [locationRestrictionId], references: [id])

  // TODO: index
  @@map("location_restriction_times")
}

model LocationDistanceRestriction {
  id                    Int             @id @default(autoincrement())
  locationRestrictionId Int             @map("location_restriction_id")
  originType            EDistanceType   @map("origin_type")
  destinationType       EDistanceType   @map("destination_type")
  calcDistanceBy        ECalcDistanceBy @map("calc_distance_by")
  minDistance           Decimal?        @map("min_distance") @db.Decimal
  maxDistance           Decimal?        @map("max_distance") @db.Decimal
  isActive              Boolean         @map("is_active")
  createdAt             DateTime?       @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy             String?         @default("SYS") @map("created_by") @db.VarChar(100)
  updatedAt             DateTime?       @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy             String?         @map("updated_by") @db.VarChar(100)

  locationRestriction LocationRestriction?             @relation(fields: [locationRestrictionId], references: [id])
  applications        LocationRestrictionApplication[]

  @@unique([locationRestrictionId, originType, destinationType, calcDistanceBy], name: "location_restriction_id_origin_type_destination_type_calc_distance_by")
  // TODO: index
  @@map("location_restriction_distances")
}

model OtpServices {
  id          Int       @id @default(autoincrement())
  code        String    @unique() @map("os_code") // EOTPCode
  name        String?   @map("os_name") @db.VarChar(100)
  description String?   @map("os_description") @db.VarChar(255)
  value       String?   @map("os_value") @db.VarChar(255)
  isStatus    Boolean?  @default(true) @map("os_is_status") @db.Boolean
  updatedAt   DateTime? @default(now()) @map("last_modified_date") @db.Timestamp(6)
  updatedBy   String    @default("SYS") @map("last_modified_by") @db.VarChar(20)

  @@map("otp_services")
}

model PurchaseLimitationMerchants {
  id                   Int                 @id @default(autoincrement())
  purchaseLimitationId Int                 @map("plm_purchase_limitation_id")
  merchantId           String              @map("plm_merchant_id") @db.VarChar(50)
  merchantName         String              @map("plm_merchant_name") @db.VarChar(255)
  storeId              String              @map("plm_store_id") @db.VarChar(50)
  storeName            String              @map("plm_store_name") @db.VarChar(255)
  updatedAt            DateTime?           @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy            String?             @default("SYS") @map("updated_by") @db.VarChar(50)
  purchaseLimitations  PurchaseLimitations @relation(fields: [purchaseLimitationId], references: [id])

  @@index([id])
  @@index([purchaseLimitationId])
  @@index([merchantId])
  @@index([storeId])
  @@index([merchantId, storeId], map: "merchant_id_and_store_id")
  @@map("purchase_limitation_merchants")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model PurchaseLimitationRules {
  id                   Int                          @id @default(autoincrement())
  purchaseLimitationId Int                          @map("plr_purchase_limitation_id")
  type                 EPurchaseLimitationRuleType  @map("plr_type")
  criteria             EPurchaseLimitationCriteria  @map("plr_criteria")
  operator             EOperator                    @map("plr_operator")
  value                String                       @map("plr_value") @db.VarChar(255)
  valueInputType       EValueTypeInput              @map("plr_value_input_type")
  timeUniq             EPurchaseLimitationTimeUniq? @map("plr_time_uniq")
  timeValue            Float?                       @map("plr_time_value") @db.Real
  activated            Boolean                      @default(false) @map("plr_activated")
  isDeleted            Boolean                      @default(false) @map("plr_is_deleted")
  createdAt            DateTime?                    @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy            String?                      @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt            DateTime?                    @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy            String?                      @default("SYS") @map("updated_by") @db.VarChar(255)
  purchaseLimitations  PurchaseLimitations          @relation(fields: [purchaseLimitationId], references: [id])

  @@index([id])
  @@index([purchaseLimitationId])
  @@map("purchase_limitation_rules")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model PurchaseLimitations {
  id             Int                           @id @default(autoincrement())
  createdAt      DateTime?                     @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy      String?                       @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt      DateTime?                     @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy      String?                       @default("SYS") @map("updated_by") @db.VarChar(50)
  paymentChannel PaymentChannel                @default(ALL) @map("pl_payment_channel")
  activated      Boolean                       @default(false) @map("pl_activated")
  isDefault      Boolean                       @default(false) @map("pl_is_default")
  merchants      PurchaseLimitationMerchants[]
  rules          PurchaseLimitationRules[]

  @@index([id])
  @@map("purchase_limitations")
}

model PlatformBlocking {
  id                            Int       @id @default(autoincrement())
  platformKey                   String    @map("platform_key") @db.VarChar(100)
  platformName                  String    @map("platform_name") @db.VarChar(100)
  isBlockingUnconfiguredSources Boolean   @default(true) @map("is_blocking_unconfigured_sources") @db.Boolean
  isBlockingUnknownSources      Boolean   @default(true) @map("is_blocking_unknown_sources") @db.Boolean // no clue what the source is. source = null
  isBlockingEnabled             Boolean   @default(true) @map("is_blocking_enabled") @db.Boolean
  displayOrder                  Int       @default(1) @map("display_order") @db.Integer
  createdAt                     DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy                     String?   @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt                     DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy                     String?   @map("updated_by") @db.VarChar(50)
  serviceMapping                String    @map("service_mapping") @db.VarChar(255)

  sources                     PlatformBlockingSource[]
  service                     Services                      @relation(fields: [serviceMapping], references: [code])
  ruleSegmentPlatformBlocking RuleSegmentPlatformBlocking[]

  @@index([platformKey])
  @@map("platform_blockings")
}

model PlatformBlockingSource {
  id                 Int       @id @default(autoincrement())
  platformBlockingId Int       @map("platform_blocking_id")
  sourceKey          String    @map("source_key") @db.VarChar(100)
  sourceName         String    @map("source_name") @db.VarChar(100)
  isBlocked          Boolean   @map("is_blocked") @db.Boolean
  displayOrder       Int       @default(1) @map("display_order") @db.Integer
  createdAt          DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy          String?   @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt          DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy          String?   @map("updated_by") @db.VarChar(50)

  platformBlocking PlatformBlocking @relation(fields: [platformBlockingId], references: [id])

  @@unique([platformBlockingId, sourceKey])
  @@index([platformBlockingId])
  @@index([sourceKey])
  @@map("blocked_platform_sources")
}

model RuleSegment {
  id          Int       @id @default(autoincrement())
  code        String    @unique @map("code") @db.VarChar(100)
  name        String?   @map("name") @db.VarChar(255)
  description String?   @map("description") @db.Text
  priority    Int       @default(1) @map("priority") @db.Integer
  isActive    Boolean   @default(false) @map("is_active") @db.Boolean
  createdBy   String?   @default("SYS") @map("created_by") @db.VarChar(50)
  createdAt   DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt   DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy   String?   @map("updated_by") @db.VarChar(50)

  conditions        RuleSegmentCondition[]
  services          RuleSegmentService[]
  locations         RuleSegmentLocation[]
  platformBlockings RuleSegmentPlatformBlocking[]

  @@index([code])
  @@map("rule_segments")
}

model RuleSegmentCondition {
  id                     Int       @id @default(autoincrement())
  ruleSegmentId          Int       @map("rule_segment_id")
  minAge                 Int?      @map("min_age") @db.Integer
  maxAge                 Int?      @map("max_age") @db.Integer
  gender                 EGender?  @map("gender")
  minIncome              Int?      @map("min_income") @db.Integer
  isLocationCheckEnabled Boolean   @default(false) @map("is_location_check_enabled") @db.Boolean
  isSourceCheckEnabled   Boolean   @default(false) @map("is_source_check_enabled") @db.Boolean
  isEligibleListEnabled  Boolean   @default(false) @map("is_eligible_list_enabled") @db.Boolean
  updatedAt              DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy              String    @map("updated_by") @db.VarChar(50)

  rule              RuleSegment                   @relation(fields: [ruleSegmentId], references: [id])
  locations         RuleSegmentLocation[]
  platformBlockings RuleSegmentPlatformBlocking[]

  @@index([ruleSegmentId])
  @@map("rule_segment_conditions")
}

model RuleSegmentLocation {
  id                     Int       @id @default(autoincrement())
  ruleSegmentId          Int       @map("rule_segment_id")
  ruleSegmentConditionId Int       @map("rule_segment_condition_id")
  provinceCode           String    @map("province_code") @db.VarChar(100)
  updatedAt              DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy              String    @map("updated_by") @db.VarChar(50)

  province  Province             @relation(fields: [provinceCode], references: [code])
  rule      RuleSegment          @relation(fields: [ruleSegmentId], references: [id])
  condition RuleSegmentCondition @relation(fields: [ruleSegmentConditionId], references: [id])

  @@unique([ruleSegmentId, provinceCode])
  @@index([ruleSegmentId])
  @@index([provinceCode])
  @@map("rule_segment_locations")
}

model RuleSegmentService {
  id            Int       @id @default(autoincrement())
  ruleSegmentId Int       @map("rule_segment_id")
  serviceCode   String    @map("service_code") @db.VarChar(100)
  isEnabled     Boolean   @default(false) @map("is_enabled") @db.Boolean
  updatedAt     DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy     String    @map("updated_by") @db.VarChar(50)

  service     Services    @relation(fields: [serviceCode], references: [code])
  ruleSegment RuleSegment @relation(fields: [ruleSegmentId], references: [id])

  @@unique([ruleSegmentId, serviceCode])
  @@index([ruleSegmentId])
  @@index([serviceCode])
  @@map("rule_segment_services")
}

model ReferenceMapping {
  id            Int            @id @default(autoincrement())
  sourceType    ESourceType    @map("source_type")
  sourceId      Int            @map("source_id")
  referenceType EReferenceType @map("reference_type")
  referenceId   Int            @map("reference_id")
  isEnabled     Boolean        @default(false) @map("is_enabled") @db.Boolean
  updatedAt     DateTime?      @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy     String         @map("updated_by") @db.VarChar(50)

  @@unique([sourceType, sourceId, referenceType, referenceId])
  @@index([sourceType, sourceId])
  @@index([referenceType, referenceId])
  @@map("reference_mappings")
}

model RuleSegmentPlatformBlocking {
  id                     Int @id @default(autoincrement())
  ruleSegmentId          Int @map("rule_segment_id")
  ruleSegmentConditionId Int @map("rule_segment_condition_id")
  platformBlockingId     Int @map("platform_blocking_id")

  updatedAt DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy String    @map("updated_by") @db.VarChar(50)

  rule             RuleSegment          @relation(fields: [ruleSegmentId], references: [id])
  condition        RuleSegmentCondition @relation(fields: [ruleSegmentConditionId], references: [id])
  platformBlocking PlatformBlocking     @relation(fields: [platformBlockingId], references: [id])

  @@unique([ruleSegmentId, platformBlockingId])
  @@index([ruleSegmentId])
  @@index([platformBlockingId])
  @@map("rule_segment_platform_blockings")
}

model UsageLimitGroup {
  id          Int       @id @default(autoincrement())
  name        String    @map("name") @db.VarChar(255)
  description String?   @map("description") @db.Text
  isDefault   Boolean   @default(false) @map("is_default") @db.Boolean
  createdAt   DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy   String?   @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt   DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy   String?   @map("updated_by") @db.VarChar(50)

  usageLimitMatrix UsageLimitMatrix[]

  @@map("usage_limit_groups")
}

model UsageLimitMatrix {
  id             Int       @id @default(autoincrement())
  groupId        Int       @map("group_id") @db.Integer
  name           String?   @map("name") @db.VarChar(255)
  minPaybackTime Int       @map("min_payback_time") @db.Integer
  usageLimit     Int       @map("usage_limit") @db.Integer
  description    String?   @map("description") @db.Text
  isActive       Boolean   @default(false) @map("is_active") @db.Boolean
  isRequired     Boolean   @default(false) @map("is_required") @db.Boolean
  createdAt      DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy      String?   @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt      DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy      String?   @map("updated_by") @db.VarChar(50)

  group UsageLimitGroup @relation(fields: [groupId], references: [id])

  @@unique([groupId, minPaybackTime])
  @@map("usage_limit_matrix")
}

model CustomerGroup {
  id          Int       @id @default(autoincrement())
  name        String    @map("name") @db.VarChar(255)
  description String?   @map("description") @db.Text
  isActive    Boolean   @default(false) @map("is_active") @db.Boolean
  updatedAt   DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy   String?   @map("updated_by") @db.VarChar(50)

  customerGroupDetails CustomerGroupDetail[]

  @@map("customer_groups")
}

model CustomerGroupDetail {
  id              Int       @id @default(autoincrement())
  customerGroupId Int       @map("customer_group_id")
  phone           String?   @map("phone") @db.VarChar(15)
  email           String?   @map("email") @db.VarChar(100)
  isActive        Boolean   @default(false) @map("is_active") @db.Boolean
  updatedAt       DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy       String?   @map("updated_by") @db.VarChar(50)

  customerGroup CustomerGroup @relation(fields: [customerGroupId], references: [id])

  @@index([email])
  @@index([phone])
  @@map("customer_group_details")
}

model RejectCode {
  id                 Int                    @id @default(autoincrement())
  stage              String?                @map("rj_stage") @db.VarChar(50)
  code               String                 @unique(map: "reject_code_pk") @map("rj_code") @db.VarChar(20)
  description        String?                @map("rj_description") @db.VarChar(500)
  timeToReSubmit     Int?                   @default(0) @map("rj_time_to_re_submit")
  timeToReSubmitType ELockTimeResubmitType? @default(day) @map("rj_time_to_re_submit_type")
  canAdjustResubmit  Boolean?               @default(false) @map("rj_can_adjust_re_submit")
  isActive           Boolean?               @default(true) @map("rj_is_status")
  updatedAt          DateTime?              @default(now()) @updatedAt @map("last_modified_date") @db.Timestamp(6)
  updatedBy          String                 @default("SYS") @map("last_modified_by") @db.VarChar(20)

  @@map("reject_code")
}

model TransactionMoneyAmount {
  id               Int       @id @default(autoincrement())
  groupDescription String    @map("tma_group_description") @db.VarChar(255)
  minLimit         Decimal   @map("tma_min_limit") @db.Decimal
  maxLimit         Decimal   @map("tma_max_limit") @db.Decimal
  maxTenor         Int       @map("tma_max_tenor") @db.SmallInt
  minTenor         Int       @default(1) @map("tma_min_tenor") @db.SmallInt
  isStatus         Boolean   @default(true) @map("tma_is_status")
  updatedAt        DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy        String    @default("SYS") @map("updated_by") @db.VarChar(20)

  @@map("transaction_money_amount")
}

model Tenures {
  id          Int       @id @default(autoincrement())
  tenure      Int       @unique @map("t_tenure") @db.SmallInt
  feeCode     String    @default("DEFAULT_CODE") @map("t_fee_code") @db.VarChar(20)
  feeValue    Decimal   @map("t_fee_value") @db.Decimal
  description String?   @map("t_description") @db.VarChar(255)
  isStatus    Boolean   @default(true) @map("t_is_status") @db.Boolean
  updatedAt   DateTime? @default(now()) @updatedAt() @map("updated_at") @db.Timestamp(6)
  updatedBy   String    @default("SYS") @map("updated_by") @db.VarChar(20)

  @@map("tenures")
}

model UserToken {
  id        Int       @id @default(autoincrement())
  username  String    @db.VarChar(80)
  userToken String?   @map("user_token") @db.VarChar(1000)
  startTime DateTime? @map("start_time") @db.Timestamp(6)
  endTime   DateTime? @map("end_time") @db.Timestamp(6)
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  users     Users     @relation(fields: [username], references: [username], onDelete: NoAction, onUpdate: NoAction, map: "user_token_users_fk")

  @@map("user_token")
}

model Users {
  id           Int         @id @default(autoincrement())
  username     String      @unique @db.VarChar(255)
  password     String      @db.VarChar(255)
  email        String?     @db.VarChar(255)
  createdAt    DateTime    @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy    String      @map("created_by") @db.VarChar(50)
  updatedAt    DateTime?   @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy    String?     @map("updated_by") @db.VarChar(50)
  isActive     Boolean     @default(true) @map("is_active")
  isChangePass Boolean     @default(false) @map("is_change_pass")
  userToken    UserToken[]

  @@map("users")
}

model Workflows {
  id               Int                @id @default(autoincrement())
  code             String             @unique @map("workflow_code") @db.VarChar(100)
  name             String?            @map("workflow_name") @db.VarChar(255)
  description      String?            @map("workflow_description") @db.VarChar(255)
  isStatus         Boolean            @default(true) @map("workflow_is_status") @db.Boolean
  updatedAt        DateTime?          @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy        String?            @map("updated_by") @db.VarChar(50)
  workflowServices WorkflowServices[] @relation("wfWorkflowSer")

  @@index([id])
  @@index([code])
  @@map("workflows")
}

model Services {
  id               Int                @id @default(autoincrement())
  code             String             @unique @map("service_code") @db.VarChar(100)
  name             String?            @map("service_name") @db.VarChar(255)
  description      String?            @map("service_description") @db.VarChar(255)
  isStatus         Boolean            @default(true) @map("service_is_status") @db.Boolean
  updatedAt        DateTime?          @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy        String?            @map("updated_by") @db.VarChar(50)
  workflowServices WorkflowServices[] @relation("WorkflowServices")

  platformBlockings   PlatformBlocking[]
  ruleSegmentServices RuleSegmentService[]

  @@index([id])
  @@index([code])
  @@map("services")
}

model WorkflowServices {
  id                        Int                      @id @default(autoincrement())
  workflowCode              String                   @map("workflow_code") @db.VarChar(100)
  serviceCode               String                   @map("service_code") @db.VarChar(100)
  isActive                  Boolean                  @default(true) @map("wf_sv_is_active") @db.Boolean
  required                  Boolean                  @default(false) @db.Boolean // Required is true means isActive must be true
  note                      String?                  @map("wf_sv_note") @db.VarChar(255)
  isConfigurableByOtherRule Boolean                  @default(false) @map("is_configurable_by_other_rule") @db.Boolean
  priority                  Int?                     @default(1) @map("priority") @db.Integer
  updatedAt                 DateTime?                @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy                 String?                  @map("updated_by") @db.VarChar(50)
  workflow                  Workflows?               @relation("wfWorkflowSer", fields: [workflowCode], references: [code])
  service                   Services?                @relation("WorkflowServices", fields: [serviceCode], references: [code])
  configs                   WorkflowServiceConfigs[]

  @@unique([workflowCode, serviceCode], name: "workflow_code_service_code")
  @@index([id])
  @@index([workflowCode])
  @@index([serviceCode])
  @@map("workflow_services")
}

model WorkflowServiceConfigs {
  id           Int       @id @default(autoincrement())
  workflowCode String    @map("workflow_code") @db.VarChar(100)
  serviceCode  String    @map("service_code") @db.VarChar(100)
  configKey    String    @map("config_key") @db.VarChar(100)
  configValue  String?   @map("config_value") @db.VarChar(255)
  description  String?   @map("description") @db.VarChar(255)
  isActive     Boolean   @default(true) @map("is_active") @db.Boolean
  updatedAt    DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy    String?   @map("updated_by") @db.VarChar(50)

  workflowServices WorkflowServices @relation(fields: [workflowCode, serviceCode], references: [workflowCode, serviceCode])

  @@unique([workflowCode, serviceCode, configKey])
  @@map("workflow_service_configs")
}

model Jobs {
  id          Int       @id @default(autoincrement())
  code        String    @unique @map("code") @db.VarChar(100)
  name        String?   @map("name") @db.VarChar(255)
  description String?   @map("description") @db.VarChar(255)
  isActive    Boolean   @default(true) @map("is_active") @db.Boolean
  updatedAt   DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy   String?   @map("updated_by") @db.VarChar(50)

  @@index([code])
  @@map("jobs")
}

model Income {
  code      String    @id @map("code")
  name      String    @map("name") @db.VarChar(255)
  value     Int       @map("value") @db.Integer
  order     Int       @default(1) @map("order") @db.Integer
  isActive  Boolean   @default(false) @map("is_active") @db.Boolean
  updatedAt DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy String?   @map("updated_by") @db.VarChar(50)

  @@map("incomes")
}

model Province {
  id        Int       @id @default(autoincrement())
  code      String    @unique @map("code") @db.VarChar(100)
  name      String?   @map("name") @db.VarChar(255)
  isActive  Boolean   @default(true) @map("is_active") @db.Boolean
  updatedAt DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy String?   @map("updated_by") @db.VarChar(50)

  districts           District[]
  wards               Ward[]
  ruleSegmentLocation RuleSegmentLocation[]

  @@index([code])
  @@map("provinces")
}

model District {
  id           Int       @id @default(autoincrement())
  provinceCode String    @map("province_code") @db.VarChar(100)
  code         String    @unique @map("code") @db.VarChar(100)
  name         String?   @map("name") @db.VarChar(255)
  isActive     Boolean   @default(true) @map("is_active") @db.Boolean
  updatedAt    DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy    String?   @map("updated_by") @db.VarChar(50)

  province Province? @relation(fields: [provinceCode], references: [code])
  wards    Ward[]

  @@index([code])
  @@map("districts")
}

model Ward {
  id           Int       @id @default(autoincrement())
  districtCode String    @map("district_code") @db.VarChar(100)
  provinceCode String    @map("province_code") @db.VarChar(100)
  code         String    @unique @map("code") @db.VarChar(100)
  name         String?   @map("name") @db.VarChar(255)
  isActive     Boolean   @default(true) @map("is_active") @db.Boolean
  updatedAt    DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy    String?   @map("updated_by") @db.VarChar(50)

  district District? @relation(fields: [districtCode], references: [code])
  province Province? @relation(fields: [provinceCode], references: [code])

  @@index([code])
  @@map("wards")
}

model Promotions {
  id                   Int            @id @default(autoincrement())
  code                 String         @unique @map("code") @db.VarChar(100)
  name                 String?        @map("name") @db.VarChar(255)
  description          String?        @map("description") @db.Text
  discountPercent      Float?         @map("discount_percent") @db.Real
  discountAmount       Float?         @map("discount_amount") @db.DoublePrecision
  budgetAmount         Float          @map("budget_amount") @db.DoublePrecision
  activeFrom           DateTime       @map("start_date") @db.Timestamp(6)
  activeTo             DateTime       @map("end_date") @db.Timestamp(6)
  hasApplyOnFirstTrans Boolean?       @default(false) @map("has_apply_on_first_trans") @db.Boolean
  paymentChannel       PaymentChannel @default(ALL) @map("payment_channel")

  // tracking promotion
  usedBudgetAmount      Float? @default(0) @map("used_budget_amount") @db.DoublePrecision
  temporarilyUsedAmount Float? @default(0) @map("temporarily_used_amount") @db.DoublePrecision // total held + actually used

  isActive Boolean @default(false) @map("is_active") @db.Boolean

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy String    @default("SYS") @map("created_by") @db.VarChar(50)
  updatedAt DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy String?   @map("updated_by") @db.VarChar(50)

  promotionApplications PromotionApplications[]

  @@index([code])
  @@map("promotions")
}

model PromotionApplications {
  id               Int                         @id @default(autoincrement())
  applicationId    String                      @map("application_id") @db.VarChar(100)
  subApplicationId String                      @map("sub_application_id") @db.VarChar(100)
  cif              String                      @map("cif") @db.VarChar(100)
  promotionId      Int                         @map("promotion_id") @db.Integer
  promotionCode    String                      @map("promotion_code") @db.VarChar(100)
  finalDiscount    Float                       @map("final_discount") @db.DoublePrecision
  status           EPromotionApplicationStatus @default(HELD) @map("status")
  reason           String?                     @map("reason") @db.VarChar(1000)
  createdAt        DateTime                    @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt        DateTime?                   @default(now()) @updatedAt @map("updated_at") @db.Timestamp(6)

  promotion Promotions? @relation(fields: [promotionId], references: [id])

  @@unique([subApplicationId, promotionId])
  @@index([subApplicationId, promotionId])
  @@index([subApplicationId, promotionId, status])
  @@map("promotion_applications")
}

enum ESourceType {
  RULE_SEGMENT_SERVICE
}

enum EReferenceType {
  USAGE_LIMIT_GROUP
}

enum PaymentChannel {
  ALL
  ONLINE
  OFFLINE
}

enum EPurchaseLimitationRuleType {
  RULE
  CONFIGURATION
}

enum EPurchaseLimitationCriteria {
  TOTAL_NUMBER_OF_TRANSACTIONS
  TOTAL_NUMBER_OF_TRANSACTIONS_BY_STORE
  TOTAL_NUMBER_OF_TRANSACTIONS_BY_MERCHANT
  TOTAL_TRANSACTION_AMOUNT
  TOTAL_TRANSACTION_AMOUNT_BY_STORE
  TOTAL_TRANSACTION_AMOUNT_BY_MERCHANT
  TOTAL_BNBL_AMOUNT
  MIN_TRANS_AMOUNT_TO_COUNT_TRANS
  TOTAL_PURCHASE_AMT_BY_COMPANY
}

enum EPurchaseLimitationTimeUniq {
  HOUR
  MINUTE
  DAY
}

enum EValueTypeInput {
  NUMERIC
  STRING
}

enum EOperator {
  EQUAL
  // NOT_EQUAL
  GREATER_THAN
  GREATER_THAN_OR_EQUAL
  LESS_THAN
  LESS_THAN_OR_EQUAL
  // CONTAINS
  // NOT_CONTAINS
  // STARTS_WITH
  // ENDS_WITH
}

enum EPromotionApplicationStatus {
  ERROR // ERROR
  PROCESSING // IN HOLD PROCESSING
  NOT_VALID_LONGER // THIS MEANS PROMOTION IS NOT VALID LONGER
  SYSTEM_CLEAR_HELD // The system is clear the held promotion due to client requests to hold another promotion
  HELD
  TRANSACTION_REFUNDED // TRANSACTION IS REFUNDED - BHOURIS INTO TO BOS
  TRANSACTION_CANCELLED // TRANSACTION IS CANCELLED DUE TO CUSTOMER DOESN'T PAY - BIS INFO TO BOS
  TRANSACTION_REJECTED // WHILE PROCESS SIGNED CONTRACT - VPB BOS KNOW
  TRANSACTION_FAILED // TRANSACTION IS FAILED DUE TO MERCHANT'S END 
  USED
  APPLYING // Transaction is in signing process 
}

enum ECompareMode {
  ALL
  FULLY
  SMART
}

enum EMerchantConfigRuleKey {
  BUYER_PHONE // Check buyer phone is different from customer phone (account phone)
  RECIPIENT_ADDRESS // Check recipient address is in black list addresses 
}

enum EDistanceType {
  STORE
  TRANSACTION
  ONBOARDING
}

enum ECalcDistanceBy {
  TRAVEL
  LINE
}

enum ELockTimeResubmitType {
  year
  month
  day
  hour
  minute
  second
}

enum IncomeCode {
  UNKNOWN
  UNDER_5M
  BETWEEN_5_10M
  BETWEEN_10_20M
  OVER_20M
}

enum EOTPCode {
  OEFT // Effect time OTP
  OLOD // Lock time(day)
  OLOT // Lock time(hour)
  ORET // Retry Times
}

enum EGender {
  MALE
  FEMALE
  OTHER
  ALL
}

enum EUsageLimitMatrixConfigKey {
  CYCLE_RESET_USAGE_LIMIT_DAY
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model PurchaseLimitationsHis {
  his_id         Int       @id @default(autoincrement()) @db.Integer
  note           String?   @map("note") @db.VarChar(255)
  id             Int?      @db.Integer
  createdAt      DateTime? @map("created_at") @db.Timestamp(6)
  createdBy      String?   @map("created_by") @db.VarChar(50)
  updatedAt      DateTime? @updatedAt @map("updated_at") @db.Timestamp(6)
  updatedBy      String?   @default("SYS") @map("updated_by") @db.VarChar(50)
  paymentChannel String?   @map("pl_payment_channel")
  activated      Boolean?  @map("pl_activated")
  isDefault      Boolean?  @map("pl_is_default")

  @@map("purchase_limitations_his")
}

model PurchaseLimitationMerchantsHis {
  his_id               Int       @id @default(autoincrement()) @db.Integer
  note                 String?   @map("note") @db.VarChar(255)
  id                   Int?      @db.Integer
  purchaseLimitationId Int?      @map("plm_purchase_limitation_id")
  merchantId           String?   @map("plm_merchant_id") @db.VarChar(50)
  merchantName         String?   @map("plm_merchant_name") @db.VarChar(255)
  storeId              String?   @map("plm_store_id") @db.VarChar(50)
  storeName            String    @map("plm_store_name") @db.VarChar(255)
  updatedAt            DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy            String?   @map("updated_by") @db.VarChar(50)

  @@map("purchase_limitation_merchants_his")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model PurchaseLimitationRulesHis {
  his_id               Int       @id @default(autoincrement()) @db.Integer
  note                 String?   @map("note") @db.VarChar(255)
  id                   Int?      @db.Integer
  purchaseLimitationId Int?      @map("plr_purchase_limitation_id")
  type                 String?   @map("plr_type")
  criteria             String?   @map("plr_criteria")
  operator             String?   @map("plr_operator")
  value                String?   @map("plr_value") @db.VarChar(255)
  valueInputType       String?   @map("plr_value_input_type")
  timeUniq             String?   @map("plr_time_uniq")
  timeValue            Float?    @map("plr_time_value") @db.Real
  activated            Boolean?  @map("plr_activated")
  isDeleted            Boolean?  @map("plr_is_deleted")
  createdAt            DateTime? @map("created_at") @db.Timestamp(6)
  createdBy            String?   @map("created_by") @db.VarChar(50)
  updatedAt            DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy            String?   @map("updated_by") @db.VarChar(255)

  @@map("purchase_limitation_rules_his")
}

model TransactionMoneyAmountHis {
  his_id           Int       @id @default(autoincrement()) @db.Integer
  note             String?   @map("note") @db.VarChar(255)
  id               Int?      @db.Integer
  groupDescription String?   @map("tma_group_description") @db.VarChar(255)
  minLimit         Decimal?  @map("tma_min_limit") @db.Decimal
  maxLimit         Decimal?  @map("tma_max_limit") @db.Decimal
  maxTenor         Int?      @map("tma_max_tenor") @db.SmallInt
  minTenor         Int?      @map("tma_min_tenor") @db.SmallInt
  isStatus         Boolean?  @map("tma_is_status")
  updatedAt        DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy        String?   @map("updated_by") @db.VarChar(20)

  @@map("transaction_money_amount_his")
}

model TenuresHis {
  his_id      Int       @id @default(autoincrement()) @db.Integer
  note        String?   @map("note") @db.VarChar(255)
  id          Int?      @db.Integer
  tenure      Int?      @map("t_tenure") @db.SmallInt
  feeCode     String?   @map("t_fee_code") @db.VarChar(20)
  feeValue    Decimal?  @map("t_fee_value") @db.Decimal
  description String?   @map("t_description") @db.VarChar(255)
  isStatus    Boolean   @map("t_is_status") @db.Boolean
  updatedAt   DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy   String    @map("updated_by") @db.VarChar(20)

  @@map("tenures_his")
}

model WorkflowServicesHis {
  his_id                    Int       @id @default(autoincrement()) @db.Integer
  note                      String?   @map("note") @db.VarChar(255)
  id                        Int?      @db.Integer
  workflowCode              String?   @map("workflow_code") @db.VarChar(100)
  serviceCode               String?   @map("service_code") @db.VarChar(100)
  isActive                  Boolean?  @map("wf_sv_is_active") @db.Boolean
  required                  Boolean?  @db.Boolean // Required is true means isActive must be true
  isConfigurableByOtherRule Boolean?  @map("is_configurable_by_other_rule") @db.Boolean
  priority                  Int?      @map("priority") @db.Integer
  hisNote                   String?   @map("wf_sv_note") @db.VarChar(255)
  updatedAt                 DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy                 String?   @map("updated_by") @db.VarChar(50)

  @@map("workflow_services_his")
}

model BlacklistItemMerchantsHis {
  his_id          Int       @id @default(autoincrement()) @db.Integer
  note            String?   @map("note") @db.VarChar(255)
  id              Int?      @db.Integer
  blacklistItemId Int?      @map("blim_blacklist_item_id")
  merchantId      String?   @map("blim_merchant_id") @db.VarChar(100)
  merchantName    String?   @map("blim_merchant_name") @db.VarChar(255)
  storeId         String?   @map("blim_store_id") @db.VarChar(100)
  storeName       String?   @map("blim_store_name") @db.VarChar(255)
  updatedAt       DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy       String?   @map("updated_by") @db.VarChar(50)

  @@map("blacklist_item_merchants_his")
}

model BlacklistItemsHis {
  his_id         Int       @id @default(autoincrement()) @db.Integer
  note           String?   @map("note") @db.VarChar(255)
  id             Int?      @db.Integer
  itemId         String?   @map("bli_item_id") @db.VarChar(50)
  itemName       String?   @map("bli_item_name") @db.VarChar(500)
  blockType      String?   @map("bli_block_type") @db.VarChar(30)
  extScore       Decimal?  @map("bli_ext_score") @db.Decimal
  minQuantity    Decimal?  @map("bli_min_quantity") @db.Decimal
  maxQuantity    Decimal?  @map("bli_max_quantity") @db.Decimal
  maxTotalAmount Decimal?  @map("bli_max_total_amount") @db.Decimal
  fromDate       DateTime? @map("bli_from_date") @db.Timestamp(6)
  toDate         DateTime? @map("bli_to_date") @db.Timestamp(6)
  activated      Boolean?  @map("bli_activated")
  createdAt      DateTime? @map("created_at") @db.Timestamp(6)
  createdBy      String?   @map("created_by") @db.VarChar(50)
  updatedAt      DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy      String?   @map("updated_by") @db.VarChar(50)
  paymentChannel String?   @map("bli_payment_channel")

  @@map("blacklist_items_his")
}

model JobsHis {
  his_id      Int       @id @default(autoincrement()) @db.Integer
  note        String?   @map("note") @db.VarChar(255)
  id          Int?      @db.Integer
  code        String?   @db.VarChar(100)
  name        String?   @db.VarChar(255)
  description String?   @db.VarChar(255)
  isActive    Boolean?  @map("is_active") @db.Boolean
  updatedAt   DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy   String?   @map("updated_by") @db.VarChar(50)

  @@map("jobs_his")
}

model PromotionsHis {
  his_id                Int       @id @default(autoincrement()) @db.Integer
  note                  String?   @map("note") @db.VarChar(255)
  id                    Int?      @db.Integer
  code                  String?   @map("code") @db.VarChar(100)
  name                  String?   @map("name") @db.VarChar(255)
  description           String?   @map("description") @db.Text
  discountPercent       Float?    @map("discount_percent") @db.Real
  discountAmount        Float?    @map("discount_amount") @db.DoublePrecision
  budgetAmount          Float?    @map("budget_amount") @db.DoublePrecision
  activeFrom            DateTime? @map("start_date") @db.Timestamp(6)
  activeTo              DateTime? @map("end_date") @db.Timestamp(6)
  hasApplyOnFirstTrans  Boolean?  @default(false) @map("has_apply_on_first_trans") @db.Boolean
  paymentChannel        String?   @map("payment_channel")
  usedBudgetAmount      Float?    @map("used_budget_amount") @db.DoublePrecision
  temporarilyUsedAmount Float?    @map("temporarily_used_amount") @db.DoublePrecision
  isActive              Boolean?  @map("is_active") @db.Boolean
  createdAt             DateTime? @map("created_at") @db.Timestamp(6)
  createdBy             String?   @map("created_by") @db.VarChar(50)
  updatedAt             DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy             String?   @map("updated_by") @db.VarChar(50)

  @@map("promotions_his")
}

model MerchantConfigHIs {
  his_id       Int       @id @default(autoincrement()) @db.Integer
  note         String?   @map("note") @db.VarChar(255)
  id           Int?      @db.Integer
  merchantId   String?   @map("merchant_id") @db.VarChar(50)
  merchantName String?   @map("merchant_name") @db.VarChar(255)
  storeId      String?   @map("store_id") @db.VarChar(50)
  storeName    String?   @map("store_name") @db.VarChar(255)
  isActive     Boolean?  @map("is_active") @db.Boolean
  createdAt    DateTime? @map("created_at") @db.Timestamp(6)
  createdBy    String?   @map("created_by") @db.VarChar(50)
  updatedAt    DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy    String?   @map("updated_by") @db.VarChar(50)

  @@map("merchant_configs_his")
}

model MerchantConfigRuleHIs {
  his_id           Int       @id @default(autoincrement()) @db.Integer
  note             String?   @map("note") @db.VarChar(255)
  id               Int?      @db.Integer
  merchantConfigId Int?      @map("merchant_config_id")
  ruleKey          String?   @map("rule_key") @db.VarChar(50)
  paymentChannel   String?   @map("payment_channel") @db.VarChar(50)
  isActive         Boolean?  @map("is_active") @db.Boolean
  createdAt        DateTime? @map("created_at") @db.Timestamp(6)
  createdBy        String?   @map("created_by") @db.VarChar(50)
  updatedAt        DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy        String?   @map("updated_by") @db.VarChar(50)

  @@map("merchant_config_rules_his")
}

model BlackListAddressHis {
  his_id            Int       @id @default(autoincrement()) @db.Integer
  note              String?   @map("note") @db.VarChar(255)
  id                Int?      @db.Integer
  address           String?   @map("address") @db.VarChar(255)
  addressNormalized String?   @map("address_normalized") @db.VarChar(255)
  hashAddress       String?   @map("hash_address") @db.VarChar(255)
  compareMode       String?   @map("compare_mode")
  isActive          Boolean?  @map("is_active") @db.Boolean
  createdAt         DateTime? @map("created_at") @db.Timestamp(6)
  createdBy         String?   @map("created_by") @db.VarChar(50)
  updatedAt         DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy         String?   @map("updated_by") @db.VarChar(50)

  @@map("blacklist_address_his")
}

model LocationRestrictionHIs {
  his_id         Int       @id @default(autoincrement()) @db.Integer
  note           String?   @map("note") @db.VarChar(255)
  id             Int?      @db.Integer
  merchantId     String?   @map("merchant_id") @db.VarChar(50)
  merchantName   String?   @map("merchant_name") @db.VarChar(255)
  storeId        String?   @map("store_id") @db.VarChar(50)
  storeName      String?   @map("store_name") @db.VarChar(255)
  lat            Decimal?  @map("lat") @db.Decimal
  lng            Decimal?  @map("lng") @db.Decimal
  applyAllTime   Boolean?  @map("apply_all_time") @db.Boolean
  paymentChannel String?   @map("payment_channel")
  isActive       Boolean?  @map("is_active") @db.Boolean
  createdAt      DateTime? @map("created_at") @db.Timestamp(6)
  createdBy      String?   @map("created_by") @db.VarChar(50)
  updatedAt      DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy      String?   @map("updated_by") @db.VarChar(50)

  @@map("location_restrictions_his")
}

model LocationRestrictionTimeHIs {
  his_id                Int       @id @default(autoincrement()) @db.Integer
  note                  String?   @map("note") @db.VarChar(255)
  id                    Int?      @db.Integer
  locationRestrictionId Int?      @map("location_restriction_id") @db.Integer
  fromHours             Int?      @map("from_hours") @db.Integer
  fromMinutes           Int?      @map("from_minutes") @db.Integer
  toHours               Int?      @map("to_hours") @db.Integer
  toMinutes             Int?      @map("to_minutes") @db.Integer
  isActive              Boolean?  @map("is_active") @db.Boolean
  createdAt             DateTime? @map("created_at") @db.Timestamp(6)
  createdBy             String?   @map("created_by") @db.VarChar(50)
  updatedAt             DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy             String?   @map("updated_by") @db.VarChar(50)

  @@map("location_restriction_times_his")
}

model LocationRestrictionDistanceHIs {
  his_id                Int       @id @default(autoincrement()) @db.Integer
  note                  String?   @map("note") @db.VarChar(255)
  id                    Int?      @db.Integer
  locationRestrictionId Int?      @map("location_restriction_id") @db.Integer
  originType            String?   @map("origin_type") @db.VarChar(50)
  destinationType       String?   @map("destination_type") @db.VarChar(50)
  calcDistanceBy        String?   @map("calc_distance_by") @db.VarChar(50)
  minDistance           Decimal?  @map("min_distance") @db.Decimal
  maxDistance           Decimal?  @map("max_distance") @db.Decimal
  isActive              Boolean?  @map("is_active") @db.Boolean
  createdAt             DateTime? @map("created_at") @db.Timestamp(6)
  createdBy             String?   @map("created_by") @db.VarChar(50)
  updatedAt             DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy             String?   @map("updated_by") @db.VarChar(50)

  @@map("location_restriction_distances_his")
}

model ProvinceHis {
  his_id    Int       @id @default(autoincrement()) @db.Integer
  note      String?   @map("note") @db.VarChar(255)
  id        Int?      @db.Integer
  code      String?   @map("code") @db.VarChar(100)
  name      String?   @map("name") @db.VarChar(255)
  isActive  Boolean?  @map("is_active") @db.Boolean
  updatedAt DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy String?   @map("updated_by") @db.VarChar(50)

  @@map("provinces_his")
}

model DistrictHis {
  his_id       Int       @id @default(autoincrement()) @db.Integer
  note         String?   @map("note") @db.VarChar(255)
  id           Int?      @db.Integer
  provinceCode String?   @map("province_code") @db.VarChar(100)
  code         String?   @map("code") @db.VarChar(100)
  name         String?   @map("name") @db.VarChar(255)
  isActive     Boolean?  @map("is_active") @db.Boolean
  updatedAt    DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy    String?   @map("updated_by") @db.VarChar(50)

  @@map("districts_his")
}

model WardHis {
  his_id       Int       @id @default(autoincrement()) @db.Integer
  note         String?   @map("note") @db.VarChar(255)
  id           Int?      @db.Integer
  districtCode String?   @map("district_code") @db.VarChar(100)
  provinceCode String?   @map("province_code") @db.VarChar(100)
  code         String?   @map("code") @db.VarChar(100)
  name         String?   @map("name") @db.VarChar(255)
  isActive     Boolean?  @map("is_active") @db.Boolean
  updatedAt    DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy    String?   @map("updated_by") @db.VarChar(50)

  @@map("wards_his")
}

model IncomeHis {
  his_id    Int       @id @default(autoincrement()) @db.Integer
  note      String?   @map("note") @db.VarChar(255)
  code      String?   @map("code")
  name      String?   @map("name") @db.VarChar(255)
  value     Int?      @map("value") @db.Integer
  order     Int?      @map("order") @db.Integer
  isActive  Boolean?  @map("is_active") @db.Boolean
  updatedAt DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy String?   @map("updated_by") @db.VarChar(50)

  @@map("incomes_his")
}

model RejectCodeHis {
  his_id             Int       @id @default(autoincrement()) @db.Integer
  note               String?   @map("note") @db.VarChar(255)
  id                 Int       @db.Integer
  stage              String?   @map("rj_stage") @db.VarChar(50)
  code               String    @map("rj_code") @db.VarChar(20)
  description        String?   @map("rj_description") @db.VarChar(500)
  timeToReSubmit     Int?      @map("rj_time_to_re_submit")
  timeToReSubmitType String?   @map("rj_time_to_re_submit_type")
  canAdjustResubmit  Boolean?  @map("rj_can_adjust_re_submit")
  isActive           Boolean?  @map("rj_is_status")
  updatedAt          DateTime? @default(now()) @updatedAt @map("last_modified_date") @db.Timestamp(6)
  updatedBy          String    @map("last_modified_by") @db.VarChar(20)

  @@map("reject_code_his")
}

model PlatformBlockingHis {
  his_id                        Int       @id @default(autoincrement()) @db.Integer
  note                          String?   @map("note") @db.VarChar(255)
  id                            Int?      @db.Integer
  platformKey                   String?   @map("platform_key") @db.VarChar(100)
  platformName                  String?   @map("platform_name") @db.VarChar(100)
  isBlockingUnconfiguredSources Boolean?  @map("is_blocking_unconfigured_sources") @db.Boolean
  isBlockingUnknownSources      Boolean?  @map("is_blocking_unknown_sources") @db.Boolean
  isBlockingEnabled             Boolean?  @map("is_blocking_enabled") @db.Boolean
  displayOrder                  Int?      @map("display_order") @db.Integer
  serviceMapping                String?   @map("service_mapping") @db.VarChar(255)
  createdAt                     DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy                     String?   @map("created_by") @db.VarChar(50)
  updatedAt                     DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy                     String?   @map("updated_by") @db.VarChar(50)

  @@map("platform_blockings_his")
}

model PlatformBlockingSourceHis {
  his_id             Int       @id @default(autoincrement()) @db.Integer
  note               String?   @map("note") @db.VarChar(255)
  id                 Int?      @db.Integer
  platformBlockingId Int?      @map("platform_blocking_id") @db.Integer
  sourceKey          String?   @map("source_key") @db.VarChar(100)
  sourceName         String?   @map("source_name") @db.VarChar(100)
  isBlocked          Boolean?  @map("is_blocked") @db.Boolean
  displayOrder       Int?      @map("display_order") @db.Integer
  createdAt          DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy          String?   @map("created_by") @db.VarChar(50)
  updatedAt          DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy          String?   @map("updated_by") @db.VarChar(50)

  @@map("blocked_platform_sources_his")
}

model RuleSegmentHis {
  his_id      Int       @id @default(autoincrement()) @db.Integer
  note        String?   @map("note") @db.VarChar(255)
  id          Int?      @db.Integer
  code        String?   @map("code") @db.VarChar(100)
  name        String?   @map("name") @db.VarChar(255)
  description String?   @map("description") @db.Text
  priority    Int?      @map("priority") @db.Integer
  isActive    Boolean?  @map("is_active") @db.Boolean
  createdAt   DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy   String?   @map("created_by") @db.VarChar(50)
  updatedAt   DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy   String?   @map("updated_by") @db.VarChar(50)

  @@map("rule_segments_his")
}

model RuleSegmentConditionHis {
  his_id                 Int       @id @default(autoincrement()) @db.Integer
  note                   String?   @map("note") @db.VarChar(255)
  id                     Int?      @db.Integer
  ruleSegmentId          Int?      @map("rule_segment_id") @db.Integer
  minAge                 Int?      @map("min_age") @db.Integer
  maxAge                 Int?      @map("max_age") @db.Integer
  gender                 String?   @map("gender") @db.VarChar(50)
  minIncome              Int?      @map("min_income") @db.Integer
  isLocationCheckEnabled Boolean?  @map("is_location_check_enabled") @db.Boolean
  isSourceCheckEnabled   Boolean?  @map("is_source_check_enabled") @db.Boolean
  isEligibleListEnabled  Boolean?  @map("is_eligible_list_enabled") @db.Boolean
  isActive               Boolean?  @map("is_active") @db.Boolean
  createdAt              DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy              String?   @map("created_by") @db.VarChar(50)
  updatedAt              DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy              String?   @map("updated_by") @db.VarChar(50)

  @@map("rule_segment_conditions_his")
}

model RuleSegmentLocationHis {
  his_id                 Int       @id @default(autoincrement()) @db.Integer
  note                   String?   @map("note") @db.VarChar(255)
  id                     Int?      @db.Integer
  ruleSegmentId          Int?      @map("rule_segment_id") @db.Integer
  ruleSegmentConditionId Int?      @map("rule_segment_condition_id") @db.Integer
  provinceCode           String?   @map("province_code") @db.VarChar(100)
  createdAt              DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy              String?   @map("created_by") @db.VarChar(50)
  updatedAt              DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy              String?   @map("updated_by") @db.VarChar(50)

  @@map("rule_segment_locations_his")
}

model RuleSegmentServiceHis {
  his_id        Int       @id @default(autoincrement()) @db.Integer
  note          String?   @map("note") @db.VarChar(255)
  id            Int?      @db.Integer
  ruleSegmentId Int?      @map("rule_segment_id") @db.Integer
  serviceCode   String?   @map("service_code") @db.VarChar(100)
  isEnabled     Boolean?  @map("is_enabled") @db.Boolean
  updatedAt     DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy     String?   @map("updated_by") @db.VarChar(50)

  @@map("rule_segment_services_his")
}

model ReferenceMappingHis {
  his_id        Int       @id @default(autoincrement()) @db.Integer
  note          String?   @map("note") @db.VarChar(255)
  id            Int?      @db.Integer
  sourceType    String?   @map("source_type")
  sourceId      Int?      @map("source_id") @db.Integer
  referenceType String?   @map("reference_type")
  referenceId   Int?      @map("reference_id") @db.Integer
  isEnabled     Boolean?  @map("is_enabled") @db.Boolean
  updatedAt     DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy     String?   @map("updated_by") @db.VarChar(50)

  @@map("reference_mappings_his")
}

model RuleSegmentPlatformBlockingHis {
  his_id                 Int       @id @default(autoincrement()) @db.Integer
  note                   String?   @map("note") @db.VarChar(255)
  id                     Int?      @db.Integer
  ruleSegmentId          Int?      @map("rule_segment_id") @db.Integer
  ruleSegmentConditionId Int?      @map("rule_segment_condition_id") @db.Integer
  platformBlockingId     Int?      @map("platform_blocking_id") @db.Integer
  updatedAt              DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy              String?   @map("updated_by") @db.VarChar(50)

  @@map("rule_segment_platform_blockings_his")
}

model UsageLimitGroupHis {
  his_id      Int       @id @default(autoincrement()) @db.Integer
  note        String?   @map("note") @db.VarChar(255)
  id          Int?      @db.Integer
  name        String?   @map("name") @db.VarChar(255)
  description String?   @map("description") @db.Text
  isActive    Boolean?  @map("is_active") @db.Boolean
  isDefault   Boolean?  @map("is_default") @db.Boolean
  createdAt   DateTime? @default(now()) @map("created_at") @db.Timestamp(6)
  createdBy   String?   @map("created_by") @db.VarChar(50)
  updatedAt   DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy   String?   @map("updated_by") @db.VarChar(50)

  @@map("usage_limit_groups_his")
}

model UsageLimitMatrixHis {
  his_id         Int       @id @default(autoincrement()) @db.Integer
  note           String?   @map("note") @db.VarChar(255)
  id             Int?      @db.Integer
  name           String?   @map("name") @db.VarChar(255)
  groupId        Int?      @map("group_id") @db.Integer
  minPaybackTime Int?      @map("min_payback_time") @db.Integer
  usageLimit     Int?      @map("usage_limit") @db.Integer
  description    String?   @map("description") @db.Text
  isActive       Boolean?  @map("is_active") @db.Boolean
  isRequired     Boolean?  @map("is_required") @db.Boolean
  createdAt      DateTime? @map("created_at") @db.Timestamp(6)
  createdBy      String?   @map("created_by") @db.VarChar(50)
  updatedAt      DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy      String?   @map("updated_by") @db.VarChar(50)

  @@map("usage_limit_matrix_his")
}

model WorkflowServiceConfigsHis {
  his_id       Int       @id @default(autoincrement()) @db.Integer
  note         String?   @map("note") @db.VarChar(255)
  id           Int?      @db.Integer
  workflowCode String?   @map("workflow_code") @db.VarChar(100)
  serviceCode  String?   @map("service_code") @db.VarChar(100)
  configKey    String?   @map("config_key") @db.VarChar(100)
  configValue  String?   @map("config_value") @db.VarChar(255)
  description  String?   @map("description") @db.VarChar(255)
  isActive     Boolean?  @map("is_active") @db.Boolean
  updatedAt    DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy    String?   @map("updated_by") @db.VarChar(50)

  @@map("workflow_service_configs_his")
}

model CustomerGroupHis {
  his_id      Int       @id @default(autoincrement()) @db.Integer
  note        String?   @map("note") @db.VarChar(255)
  id          Int?      @db.Integer
  name        String?   @map("name") @db.VarChar(255)
  description String?   @map("description") @db.Text
  isActive    Boolean?  @map("is_active") @db.Boolean
  updatedAt   DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy   String?   @map("updated_by") @db.VarChar(50)

  @@map("customer_groups_his")
}

model CustomerGroupDetailHis {
  his_id          Int       @id @default(autoincrement()) @db.Integer
  note            String?   @map("note") @db.VarChar(255)
  id              Int?      @db.Integer
  customerGroupId Int?      @map("customer_group_id") @db.Integer
  phone           String?   @map("phone") @db.VarChar(15)
  email           String?   @map("email") @db.VarChar(100)
  isActive        Boolean?  @map("is_active") @db.Boolean
  updatedAt       DateTime? @default(now()) @map("updated_at") @db.Timestamp(6)
  updatedBy       String?   @map("updated_by") @db.VarChar(50)

  @@map("customer_group_details_his")
}
