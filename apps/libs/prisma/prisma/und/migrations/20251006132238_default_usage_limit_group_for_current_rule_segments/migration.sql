INSERT INTO "reference_mappings" (
  "source_type",
  "source_id",
  "reference_type",
  "reference_id",
  "is_enabled",
  "updated_by"
)
SELECT
  'RULE_SEGMENT_SERVICE' AS source_type,
  rule_segment_services.id AS source_id,
  'USAGE_LIMIT_GROUP' AS reference_type,
  usage_limit_groups.id AS reference_id,
  true AS is_enabled,
  'SYS' as updated_by
FROM rule_segment_services
JOIN usage_limit_groups ON usage_limit_groups.is_default = true
WHERE rule_segment_services.service_code = 'CHECK_USAGE_LIMIT';