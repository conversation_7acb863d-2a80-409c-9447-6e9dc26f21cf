-- DropIndex
DROP INDEX "customer_group_details_customer_group_id_phone_email_key";

-- CreateIndex
CREATE INDEX "customer_group_details_email_idx" ON "customer_group_details"("email");

-- CreateIndex
CREATE INDEX "customer_group_details_phone_idx" ON "customer_group_details"("phone");

-- AddForeignKey
ALTER TABLE "customer_group_details" ADD CONSTRAINT "customer_group_details_customer_group_id_fkey" FOREIGN KEY ("customer_group_id") REFERENCES "customer_groups"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
