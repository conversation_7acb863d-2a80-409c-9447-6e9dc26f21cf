-- CreateEnum
CREATE TYPE "ESourceType" AS ENUM ('RULE_SEGMENT_SERVICE');

-- CreateEnum
CREATE TYPE "EReferenceType" AS ENUM ('USAGE_LIMIT_GROUP');

-- CreateTable
CREATE TABLE "reference_mappings" (
    "id" SERIAL NOT NULL,
    "source_type" "ESourceType" NOT NULL,
    "source_id" INTEGER NOT NULL,
    "reference_type" "EReferenceType" NOT NULL,
    "reference_id" INTEGER NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT false,
    "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" VARCHAR(50) NOT NULL,

    CONSTRAINT "reference_mappings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "reference_mappings_source_type_source_id_idx" ON "reference_mappings"("source_type", "source_id");

-- CreateIndex
CREATE INDEX "reference_mappings_reference_type_reference_id_idx" ON "reference_mappings"("reference_type", "reference_id");

-- Create<PERSON>ndex
CREATE UNIQUE INDEX "reference_mappings_source_type_source_id_reference_type_ref_key" ON "reference_mappings"("source_type", "source_id", "reference_type", "reference_id");
