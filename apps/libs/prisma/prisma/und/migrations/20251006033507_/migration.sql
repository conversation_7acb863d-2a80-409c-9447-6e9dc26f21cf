-- CreateTable
CREATE TABLE "reference_mappings_his" (
    "his_id" SERIAL NOT NULL,
    "note" VARCHAR(255),
    "id" INTEGER,
    "source_type" TEXT,
    "source_id" INTEGER,
    "reference_type" TEXT,
    "reference_id" INTEGER,
    "is_enabled" BOOLEAN,
    "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" VARCHAR(50),

    CONSTRAINT "reference_mappings_his_pkey" PRIMARY KEY ("his_id")
);
