-- CreateTable
CREATE TABLE "customer_groups" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" VARCHAR(50),

    CONSTRAINT "customer_groups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_group_details" (
    "id" SERIAL NOT NULL,
    "customer_group_id" INTEGER NOT NULL,
    "phone" VARCHAR(15),
    "email" VARCHAR(100),
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" VARCHAR(50),

    CONSTRAINT "customer_group_details_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_groups_his" (
    "his_id" SERIAL NOT NULL,
    "note" VARCHAR(255),
    "id" INTEGER,
    "name" VARCHAR(255),
    "description" TEXT,
    "is_active" BOOLEAN,
    "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" VARCHAR(50),

    CONSTRAINT "customer_groups_his_pkey" PRIMARY KEY ("his_id")
);

-- CreateTable
CREATE TABLE "customer_group_details_his" (
    "his_id" SERIAL NOT NULL,
    "note" VARCHAR(255),
    "id" INTEGER,
    "customer_group_id" INTEGER,
    "phone" VARCHAR(15),
    "email" VARCHAR(100),
    "is_active" BOOLEAN,
    "updated_at" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" VARCHAR(50),

    CONSTRAINT "customer_group_details_his_pkey" PRIMARY KEY ("his_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "customer_group_details_customer_group_id_phone_email_key" ON "customer_group_details"("customer_group_id", "phone", "email");
