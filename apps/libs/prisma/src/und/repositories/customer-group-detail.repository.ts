import { Prisma } from '@libs/prisma/client-und'
import { Injectable } from '@nestjs/common'
import { PrismaService } from '../../prisma.service'
import { UNDBaseRepository } from './base.repository'

@Injectable()
export class CustomerGroupDetailRepository extends UNDBaseRepository<Prisma.CustomerGroupDetailDelegate> {
  constructor(prisma: PrismaService) {
    super(prisma, prisma.und.customerGroupDetail)
  }
}
