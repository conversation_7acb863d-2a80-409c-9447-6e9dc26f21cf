import { Module } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { BlacklistAddressRepository } from './repositories/black-list-address.repository'
import { BlacklistItemMerchantRepository } from './repositories/black-list-item-merchant.repository'
import { BlackListItemRepository } from './repositories/black-list-item.repository'
import { DistrictRepository } from './repositories/district.repository'
import { BlackListIdCardRepository } from './repositories/id-card-black-list.repository'
import { IncomeRepository } from './repositories/income.repository'
import { JobsRepository } from './repositories/job-repository'
import { LocationRestrictionDistanceRepository } from './repositories/location-distance-restriction.repository'
import { LocationRestrictionApplicationsRepository } from './repositories/location-restriction-applications-repository'
import { LocationRestrictionRepository } from './repositories/location-restriction-repository'
import { LocationRestrictionTimeRepository } from './repositories/location-time-restriction-repository'
import { MerchantConfigRuleRepository } from './repositories/merchant-config-rule.repository'
import { MerchantConfigRepository } from './repositories/merchant-config.repository'
import { OTPServiceRepository } from './repositories/otp-service.repository'
import { BlackListPhoneRepository } from './repositories/phone-black-list.repository'
import { PlatformBlockingSourceRepository } from './repositories/platform-blocking-source.repository'
import { PlatformBlockingRepository } from './repositories/platform-blocking.repository'
import { PromotionApplicationsRepository } from './repositories/promotion-application.repository'
import { PromotionRepository } from './repositories/promotion.repository'
import { ProvinceRepository } from './repositories/province.repository'
import { PurchaseLimitationMerchantRepository } from './repositories/purchase-limitation-merchant-repository'
import { PurchaseLimitationRepository } from './repositories/purchase-limitation-repository'
import { PurchaseLimitationRuleRepository } from './repositories/purchase-limitation-rule-repository'
import { RejectCodeRepository } from './repositories/reject-code.repository'
import { RuleSegmentConditionRepository } from './repositories/rule-segment-condition.repository'
import { RuleSegmentServiceRepository } from './repositories/rule-segment-service.repository'
import { RuleSegmentRepository } from './repositories/rule-segment.repository'
import { ReferenceMappingRepository } from './repositories/reference-mappings.repository'
import { ServiceRepository } from './repositories/service.repository'
import { TenureRepository } from './repositories/tenure.repository copy'
import { TransactionMoneyAmountRepository } from './repositories/transaction-amount.respository'
import { UsageLimitGroupRepository } from './repositories/usage-limit-group.repository'
import { UsageLimitMatrixRepository } from './repositories/usage-limit-matrix.repository'
import { UserTokenRepository } from './repositories/user-token-repository'
import { UserRepository } from './repositories/user.repository'
import { WardRepository } from './repositories/ward.repository'
import { WorkflowServiceConfigRepository } from './repositories/workflow-service-config.repository'
import { WorkflowServiceRepository } from './repositories/workflow-service.repository'
import { WorkflowRepository } from './repositories/workflow.repository'
import { UnitOfWorkUnd } from './unit-of-work'
import { CustomerGroupRepository } from './repositories/customer-group.repository'
import { CustomerGroupDetailRepository } from './repositories/customer-group-detail.repository'

@Module({
  providers: [
    PrismaService,
    UnitOfWorkUnd,
    OTPServiceRepository,
    RejectCodeRepository,
    BlackListPhoneRepository,
    BlackListIdCardRepository,
    TransactionMoneyAmountRepository,
    BlackListItemRepository,
    BlacklistItemMerchantRepository,
    PurchaseLimitationRepository,
    PurchaseLimitationMerchantRepository,
    PurchaseLimitationRuleRepository,
    UserRepository,
    UserTokenRepository,
    TenureRepository,
    WorkflowServiceRepository,
    WorkflowRepository,
    JobsRepository,
    PromotionRepository,
    PromotionApplicationsRepository,
    MerchantConfigRepository,
    MerchantConfigRuleRepository,
    BlacklistAddressRepository,
    LocationRestrictionRepository,
    LocationRestrictionTimeRepository,
    LocationRestrictionDistanceRepository,
    LocationRestrictionApplicationsRepository,
    ProvinceRepository,
    DistrictRepository,
    WardRepository,
    IncomeRepository,
    ServiceRepository,
    PlatformBlockingRepository,
    PlatformBlockingSourceRepository,
    RuleSegmentRepository,
    RuleSegmentConditionRepository,
    RuleSegmentServiceRepository,
    UsageLimitMatrixRepository,
    WorkflowServiceConfigRepository,
    UsageLimitGroupRepository,
    ReferenceMappingRepository,
    CustomerGroupRepository,
    CustomerGroupDetailRepository,
  ],
  exports: [
    UnitOfWorkUnd,
    OTPServiceRepository,
    RejectCodeRepository,
    BlackListPhoneRepository,
    BlackListIdCardRepository,
    TransactionMoneyAmountRepository,
    BlackListItemRepository,
    BlacklistItemMerchantRepository,
    PurchaseLimitationRepository,
    PurchaseLimitationMerchantRepository,
    PurchaseLimitationRuleRepository,
    UserRepository,
    UserTokenRepository,
    TenureRepository,
    WorkflowServiceRepository,
    WorkflowRepository,
    JobsRepository,
    PromotionRepository,
    PromotionApplicationsRepository,
    MerchantConfigRepository,
    MerchantConfigRuleRepository,
    BlacklistAddressRepository,
    LocationRestrictionRepository,
    LocationRestrictionTimeRepository,
    LocationRestrictionDistanceRepository,
    LocationRestrictionApplicationsRepository,
    ProvinceRepository,
    DistrictRepository,
    WardRepository,
    IncomeRepository,
    ServiceRepository,
    PlatformBlockingRepository,
    PlatformBlockingSourceRepository,
    RuleSegmentRepository,
    RuleSegmentConditionRepository,
    RuleSegmentServiceRepository,
    UsageLimitMatrixRepository,
    WorkflowServiceConfigRepository,
    UsageLimitGroupRepository,
    ReferenceMappingRepository,
    CustomerGroupRepository,
    CustomerGroupDetailRepository,
  ],
})
export class UNDModule {}
