syntax = "proto3";
package contract_portion;
import "google/protobuf/timestamp.proto";


message instalmentPortion {
  optional int32 id = 1;
  optional string portionNumber = 2;
  optional string openDate = 3;
  optional string dueDate = 4;
  optional double portionAmount = 5;
  optional double principalAmount = 6;
  optional double feeAmount = 7;
  optional double paidAmount = 8;
  optional string status = 9;
  optional string instalmentPlan = 11;
  optional string instalmentChainIdt = 12;
  optional string contract = 13;
  optional string registrationNumber = 14;
  optional string billingContract = 15;
  optional double amount = 16;
  optional double fee = 17;
  optional string creationDate = 18;
  optional double instalmentPortionFee = 19;
  optional string writtenOffAmount = 20;
  optional string currency = 21;
  optional string principalPaidAmount = 22;
}

message contractPortionResponse {
  repeated instalmentPortion items = 1;
}

message contractPortionRequest{
  string billingContract = 1;
}