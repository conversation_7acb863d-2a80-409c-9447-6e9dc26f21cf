syntax = "proto3";
package customer_onboard_info;

import "modules/lpms/onboarding/evaluation-process.proto";

enum ContractType {
  UNKNOWN = 0;
  FIRST_PAGE = 1;
  STATIC_PAGES = 2;
  SIGNATURE_PAGE = 3;
  PRESIGNED_PAGE = 4;
  SIGNED_PAGE = 5;
}

message OnBoardInfo {
  optional string name = 1;
  optional string dob = 2;
  optional string address = 3;
  optional string job = 4;
  optional string identificationNumber = 5;
  optional string identificationDate = 6;
  optional string identificationExpireDate = 7;
  optional string rangeSalary = 8;
  optional string applicationId = 9;
  optional string appicationStatus = 10;
  optional int32 updatedAt = 11;
  optional int32 reOnboard = 12;
  optional int32 lastDayCanSign = 13;
  optional int32 dayCanResubmit = 14;
  optional double timeToResubmit = 15;
  optional string income = 16;
  repeated CustomerReference customerReferences = 17;
  optional string bnplProvider = 18;
}

message CustomerReference {
  optional string name = 1;
  optional string phone = 2;
  optional string relationship = 3;
}

message ContractLink {
  optional string link = 1;
  optional string type = 2;
}

message EkycInfo {
  optional string eKycStatus = 1;
  optional int32 updatedAt = 2;
}

message MainContractInfo {
  optional string cif = 1;
  repeated ContractLink contractLink = 2;
  optional string mainContractNumber = 3;
  optional int32 signedDate = 4;
}

message GetCustomerOnboardInfo {
  OnBoardInfo onBoardInfo = 1;
  EkycInfo ekycInfo = 2;
  MainContractInfo mainContractInfo = 3;
}