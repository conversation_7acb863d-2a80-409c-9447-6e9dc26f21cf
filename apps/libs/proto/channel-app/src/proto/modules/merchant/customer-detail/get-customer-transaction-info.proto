syntax = "proto3";
package customer_transaction_info;

enum TransactionStatus {
    PROCESSING = 0;
    FAILED = 1;
    SUCCESS = 2;
    REFUNDED = 3;
}

message GetCustomerTransactionInfo {
    optional string merchantTransactionId = 1;
    optional string rrn = 2;
    optional string merchantName = 3;
    optional string storeId = 4; //subAcquiring contract
    optional double totalAmount = 5; 
    optional int32 tenure = 6; 
    optional TransactionStatus transactionStatus = 7; 
    optional int32 createdAt = 8; 
    optional int32 updatedAt = 9; 
} 