syntax = "proto3";
package customer_personal_info;

import "modules/merchant/customer-detail/get-customer-onboard-info.proto";

enum AccountStatus {
  ACTIVE = 0;
  LOCKED = 1;
  DELETED = 2;
}

enum TermPolicyType {
  POLICY = 0;
  TERM = 1;
  UNKNOWN = 3;
}

message TermPolicy {
  optional TermPolicyType type = 1;
  optional int32 id = 2;
  optional int32 acceptedDate = 3;
}

message GetCustomerPersonalInfo {
  optional string customerName = 1;
  optional int32 registerDate = 2;
  optional AccountStatus status = 3;
  optional string email = 4;
  optional string platform = 5;
  optional string deleteReason = 6;
  repeated TermPolicy termPolicy = 7;
  optional string deviceToken = 8;
  optional string mobile = 9;
  optional string contractUrl = 10;
  optional Contract contract = 11;
  repeated Device devices = 12;
  optional AccountStatus statusLimit = 13;
  optional double availableLimit = 14;
  optional double totalLimit = 15;
}

message Contract {
  optional string contractNumber = 1;
  optional int32 signDate = 2;
  optional int32 signLastDate = 3;
  optional bool isDeleted = 4;
  optional string cif = 5;
  optional string providerCif = 6;
  optional string caAccountNo = 7;
  string signedContractUrl = 8;
  string preSignedContractUrl = 9;
}

message Device {
  string deviceId = 1;
  string os = 2;
  bool isDeleted = 3;
}

