syntax = "proto3";
package onboard_source;

enum AcquisitionSource {
  pg = 0;
  directBnpl = 1;
  directBnplNoPaybackTime = 2;
  before2307 = 3;
}

enum InstallChannel {
  facebook = 0;
  google = 1;
  tiktok = 2;
  unattributed = 3;
  rentracks = 4;
  others = 5;
}

enum OnboardingSourceDataLogType {
  CREATE = 0;
  UPDATE = 1;
  DELETE = 2;
  KEEP_SOURCE = 3;
}

message OnboardSourceDataLog {
  string phone = 1;
  optional AcquisitionSource source = 2;
  string createdAt = 3;
  OnboardingSourceDataLogType type = 4;
  optional string note = 5;
  optional string location = 6;
  int32 updatedBy = 7;
  string updatedUsername = 8;
  optional string fullname = 9;
}

message GetOnboardSourceDataLogListRequest {
  int32 pageSize = 1;
  int32 page = 2;
  optional string phone = 3;
  optional string startDate = 4;
  optional string endDate = 5;
}

message GetOnboardSourceDataLogListResponse {
  repeated OnboardSourceDataLog items = 1;
  int32 pageSize = 2;
  int32 page = 3;
  int32 total = 4;
}