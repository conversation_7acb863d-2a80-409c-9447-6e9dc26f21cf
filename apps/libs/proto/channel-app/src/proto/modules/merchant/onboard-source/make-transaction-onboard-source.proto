syntax = "proto3";
package onboard_source;

import "modules/merchant/onboard-source/get-onboard-source-data-log.proto";

message InstallChannelPayload {
  optional onboard_source.InstallChannel initialInstallChannel = 1;
  optional onboard_source.InstallChannel currentInstallChannel = 2;
}

message MakeTransactionOnboardSourcePayload {
  optional onboard_source.AcquisitionSource acquisitionSource = 1;
  optional onboard_source.InstallChannelPayload installChannel = 2;
}
