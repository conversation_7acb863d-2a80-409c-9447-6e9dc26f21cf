syntax = "proto3";
package customer_note;

message GetCustomerNoteRequest {
  optional int32 customerId = 1;
  int32 pageSize = 2;
  int32 page = 3;
  string orderBy = 4;
}

message GetCustomerNoteResponse {
  repeated Note items = 1;
  int32 page = 2;
  int32 pageSize = 3;
  int32 total = 4;
}

message Note {
  int32 id = 1;
  string content = 2;
  string type = 3;
  int32 createdAt = 4;
  int32 createdBy = 5;
}