syntax = "proto3";
package onboard_source;

import "modules/merchant/onboard-source/get-onboard-source-data-log.proto";
import "modules/shared/exception.proto";

message CreateNewOnboardSourceDataRequest {
  string phone = 1;
  optional AcquisitionSource source = 2;
  string updateUsername = 3;
  int32 updateBy = 4;
  optional string note = 5;
  optional string location = 6;
  optional string fullname = 7;
}

message CreateNewOnboardSourceDataResponse {
  bool isSuccess = 1;
  optional shared.Exception exception = 2;
}