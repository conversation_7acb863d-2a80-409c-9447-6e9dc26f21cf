syntax = "proto3";
package cnf_job;

message CreateOrUpdateJobRequest {
    string name = 1;
    string cronTime = 2;
    string taskType = 3;
    bool isActive = 4;
    optional string dependency = 5;
}

message CreateOrUpdateJobResponse {
    string message = 1;
}

message GetJobsRequest {
    optional string name = 1;
    optional string taskType = 2;
    optional bool isActive = 3;
    int32 page = 4;
    int32 pageSize = 5;
}

message GetJobsResponse {
    message Job {
        string id = 1;
        string name = 2;
        string cronTime = 3;
        bool isActive = 4;
        string taskType = 5;
        string dependency = 6;
        string createdAt = 7;
    }
    repeated Job jobs = 1;
    int32 page = 2;
    int32 pageSize = 3;
    int32 total = 4;
}

message JobStatusRequest {
    string jobName = 1;
    bool isActive = 2;
}

message JobStatusResponse {
    string message = 1;
}

message CreateJobRequest {
    string name = 1;
    string cronTime = 2;
    string taskType = 3;
    bool isActive = 4;
    optional string dependency = 5;
}

message CreateJobResponse {
    string message = 1;
}

message UpdateJobRequest {
    string name = 1;
    optional string cronTime = 2;
    optional bool isActive = 4;
}

message UpdateJobResponse {
    string message = 1;
}

message GetJobByIdRequest {
    string id = 1;
}

message GetJobByIdResponse {
    string id = 1;
    string name = 2;
    string cronTime = 3;
    bool isActive = 4;
    string taskType = 5;
    string dependency = 6;
    string createdAt = 7;
}

message RunJobManuallyRequest {
    string jobName = 1;
    string date = 2;
}

message RunJobManuallyResponse {
    string message = 1;
}