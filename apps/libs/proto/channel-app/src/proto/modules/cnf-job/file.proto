syntax = "proto3";
package cnf_job;

message DownloadFileRequest {
    string fileName = 1;
    string fileType = 2;
}

message DownloadFileResponse {
    bytes fileContent = 1;
}

message GetFileLogsRequest {
    string fileName = 1;
}

message GetFileLogsResponse {
    repeated string logs = 1;
}

message ManualPullFileRequest {
    string jobName = 1;
    optional string jobDate = 2;
}

message ManualPullFileResponse {
    bool success = 1;
}

message ManualPushFileRequest {
    string jobName = 1;
    optional string jobDate = 2;
}

message ManualPushFileResponse {
    bool success = 1;
}

message GetReportFileLogsRequest {
    optional string date = 1;
    optional string startDate = 2;
    optional string endDate = 3;
    optional string jobName = 4;
    optional string status = 5;
    int32 page = 6;
    int32 pageSize = 7;
    optional string startCreatedDate = 8;
    optional string endCreatedDate = 9;
}

message GetReportFileLogsResponse {
    message FileLog {
        string fileDate = 1;
        string fileName = 2;
        string status = 3;
        int32 totalRecord = 4;
        string createdAt = 5;
    }
    repeated FileLog files = 1;
    int32 page = 2;
    int32 pageSize = 3;
    int32 total = 4;
}
