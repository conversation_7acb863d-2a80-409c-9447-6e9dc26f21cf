syntax = "proto3";
package cnf_job;

message SftpConfig {
    string id = 1;
    string host = 2;
    int32 port = 3;
    string username = 4;
    string password = 5;
    string remotePath = 6;
}

message GetSftpConfigsRequest {}

message GetSftpConfigsResponse {
    repeated SftpConfig configs = 1;
}

message UpdateSftpConfigByIdRequest {
    string id = 1;
    SftpConfig config = 2;
}

message UpdateSftpConfigByIdResponse {
    SftpConfig config = 1;
}

message GetSftpConfigByNameRequest {
    string name = 1;
}

message GetSftpConfigByNameResponse {
    SftpConfig config = 1;
}
