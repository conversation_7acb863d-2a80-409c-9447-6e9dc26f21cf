syntax = "proto3";

package lpms_disbursement_confirmation;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message DisbursementConfirmationRequest {
  string applicationId = 1;
  bool isSuccess = 2;
}

message DisbursementConfirmationResponse {
  lpms_enum.ELpmsResponseStatus status = 1;
  DisbursementConfirmationDataResponse data = 2;
}

message DisbursementConfirmationDataResponse {
  optional string expiredIn = 1;
  optional lpms_reject_code.RejectCode rejectCode = 2;
  string applicationId = 3;
}