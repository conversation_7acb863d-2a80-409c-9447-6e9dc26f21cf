syntax = "proto3";

package lpms_evaluation_process_callback;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message OnboardingEvaluationProcessCallbackRequest {
  lpms_enum.ELpmsResponseStatus status = 1;
  OnboardingEvaluationProcessCallbackDataRequest data = 2;
}

message OnboardingEvaluationProcessCallbackDataRequest {
  lpms_enum.ApplicationStatus status = 1;
  lpms_enum.FiLicense fiLicense = 2;
  string applicationId = 3;
  string group = 4;
  optional lpms_reject_code.RejectCode rejectCode = 5;
  optional string expiredIn = 6;
  optional string lastDayCanSign = 7;
  optional string unsignedContractUrl = 8;
}