syntax = "proto3";

package lpms_check_id_rules;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message CheckCardIdRulesRequest {
    string nationalId = 1;
    string phone = 2;
}

message CheckCardIdRulesResponse {
    lpms_enum.ELpmsResponseStatus status = 1;
    CheckCardIdRulesDataResponse data = 2;
}


message CheckCardIdRulesDataResponse {
    optional string expiredIn = 1;
    optional lpms_reject_code.RejectCode rejectCode = 2;
}