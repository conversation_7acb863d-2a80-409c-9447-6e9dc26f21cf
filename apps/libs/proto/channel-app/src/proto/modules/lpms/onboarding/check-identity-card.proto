syntax = "proto3";

package lpms_check_identity_card;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message CheckIdentityCardRequest {
    string nationalId = 1;
}

message CheckIdentityCardResponse {
    lpms_enum.ELpmsResponseStatus status = 1;
    CheckIdentityCardDataResponse data = 2;
}


message CheckIdentityCardDataResponse {
    optional string expiredIn = 1;
    optional lpms_reject_code.RejectCode rejectCode = 2;
}