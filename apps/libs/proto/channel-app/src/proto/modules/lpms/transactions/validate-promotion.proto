syntax = "proto3";

package lpms_validate_promotion;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message ValidatePromotionResponse {
  lpms_enum.ELpmsResponseStatus status = 1;
  ValidatePromotionDataResponse data = 2;
}

message PromotionDataPayload {
  string code = 1;
  double finalDiscount = 2;
}

message ValidatePromotionRequest {
  string subApplicationId = 1;
  PromotionDataPayload promotion = 2;
  int32 tenure = 3;
}

message PromotionDetail {
  string code = 1;
  bool isValid = 2;
}

message ValidatePromotionDataResponse {
  optional string expiredIn = 1;
  optional lpms_reject_code.RejectCode rejectCode = 2;
  InvalidPromotionData data = 3;
}

message InvalidPromotionData {
  string stage = 1;
  string code = 2;
  string desc = 3;
  repeated PromotionDetail promotions = 4;
}
