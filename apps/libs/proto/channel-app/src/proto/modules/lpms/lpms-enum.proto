syntax = "proto3";

package lpms_enum;

enum ELpmsResponseStatus {
  REJECT = 0;
  PASS = 1;
}

enum EEkycImageType {
  SELFIE = 0;
  NATIONAL_FRONT = 1;
  NATIONAL_BACK = 2;
  // Add more types as needed
}

enum ApplicationStatus {
  PROCESSING = 0;
  APPROVED = 1;
  REJECTED = 2;
  CANCELLED = 3;
  SIGNING = 4;
  SIGNED = 5;
}

enum FiLicense {
  Y = 0;
  N = 1;
}


enum EPaymentChannel {
  WEB = 0;
  POS = 1;
  APP = 2;
  WEBPOS = 3;
} 
