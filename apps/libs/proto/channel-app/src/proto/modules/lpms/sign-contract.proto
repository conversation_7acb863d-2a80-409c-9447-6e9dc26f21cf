syntax = "proto3";

package lpms_sign_contract;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message GenerateSignContractOtpRequest { string applicationId = 1; }

message GenerateSignContractOtpData {
  string sessionId = 1;
  bool isLocked = 2;
  string expiredIn = 3;
  int32 remain = 4;
  optional lpms_reject_code.RejectCode rejectCode = 5;
  optional string requestId = 6;
  optional string code = 7;
  optional string message = 8;
}

message GenerateSignContractOtpResponse {
  bool success = 1;
  optional GenerateSignContractOtpData data = 2;
}

message SignContractRequest {
  string otpValue = 1;
  string applicationId = 2;
  string requestId = 3; // otpSessionId of the VPB
}

message SignContractResponse {
  lpms_enum.ELpmsResponseStatus status = 1;
  optional SignContractResponseData data = 2;
  optional string applicationId = 3;
}

message SignContractResponseData {
  bool isOtpVerified = 1;
  optional string expiredIn = 2;
  optional lpms_reject_code.RejectCode rejectCode = 3;
  optional string message = 4;
}