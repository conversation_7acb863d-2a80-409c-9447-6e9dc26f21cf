syntax = "proto3";

package lpms_sign_contract_result_callback;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message SignContractResultCallbackRequest {
  lpms_enum.ELpmsResponseStatus status = 1;
  SignContractResultCallbackDataRequest data = 2;
}

message SignContractResultCallbackDataRequest {
  optional string signedContractUrl = 1;
  string applicationId = 2;
  optional double totalLimit = 3;
  lpms_enum.ApplicationStatus status = 4;
  optional string contractNumber = 5;
  optional string providerCif = 6;
  optional lpms_reject_code.RejectCode rejectCode = 7;
  optional string expiredIn = 8;
  optional string caNumber = 9;
  string jarvisId = 10;
}