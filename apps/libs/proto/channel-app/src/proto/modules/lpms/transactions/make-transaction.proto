syntax = "proto3";

package lpms_make_transaction;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";
import "modules/merchant/onboard-source/make-transaction-onboard-source.proto";

message MakeTransactionResponse {
  lpms_enum.ELpmsResponseStatus status = 1;
  MakeTransactionDataResponse data = 2;
}

message MakeTransactionDataResponse {
  optional string expiredIn = 1;
  optional lpms_reject_code.RejectCode rejectCode = 2;
  string applicationId = 3;
}

message MakeTransactionRequest {
  OrderInformation orderInformation = 1;
  CustInformation custInformation = 2;
  BuyerInformation buyerInformation = 3;
  repeated RefCustomer refCustomers = 4;
  optional onboard_source.MakeTransactionOnboardSourcePayload
      onboardingSourceData = 5;
}

message OrderInformation {
  string merchantId = 1;
  string subAcquiringContract = 2;
  double totalAmount = 3;
  repeated Item items = 4;
  string rrn = 5;
  string refMerchantOrderId =
      6; // order Id of merchant pass to LCF when generate QR
  string mccCode = 7;
  string transactionRefNo = 8;
  string mainAccount = 9;
  string virtualAccNo = 10;
  string virtualAltKey = 11;
  lpms_enum.EPaymentChannel paymentChannel = 12;
  string merchantCif = 13;
  string merchantPartnerCode = 14;
}

message Item {
  string skuId = 1;
  double quantity = 2;
  optional string category = 3;
  double totalAmount = 4;
  double price = 5;
  string name = 6;
}

message CustInformation {
  string cif = 1;
  string nationalId = 2;
  optional Location location = 3;
}

message Location {
  optional double lat = 1;
  optional double lng = 2;
}

message BuyerInformation {
  string name = 1;
  string phoneNo = 2;
  string email = 3;
}

message RefCustomer {
  string email = 1;
  string name = 2;
  string phoneNo = 3;
  Address address = 4;
}

message Address {
  string street = 2;
  string city = 3;
  string district = 4;
  string ward = 5;
  string zipCode = 6;
  string fullAddress = 7;
}