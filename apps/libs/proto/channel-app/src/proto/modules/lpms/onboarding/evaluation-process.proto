syntax = "proto3";

package lpms_evaluation_process;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

enum Sex {
  Male = 0;
  Female = 1;
}

enum CustomerReferenceRelationship {
  COLLEAGUE = 0;
  FRIENDS = 1;
  OTHERS = 2;
  PARENTS = 3;
  SIBLINGS = 4;
  CHILDREN = 5;
  SPOUSE = 6;
  RELATIVES = 7;
}

message CardInfo {
  string nationalId = 4;
  string issueDate = 5;
  string issuePlace = 6;
  optional string expiryDate = 7;
}

message AddressInfo {
  string province = 1;
  string provinceCode = 2;
  string district = 3;
  string districtCode = 4;
  string ward = 5;
  string wardCode = 6;
  string street = 7;
}

message CustomerReference {
  string fullName = 2;
  string phone = 3;
  CustomerReferenceRelationship relationship = 4;
}

message OnboardingEvaluationProcessRequest {
  int32 customerId = 1; // Customer Id Of Channel App
  AddressInfo currentAddress = 2;
  optional AddressInfo permanentAddress = 3;
  optional string employeeStatus = 4;
  optional string companyName = 5;
  int32 income = 6;
  string email = 7;
  CustomerReference refRelationship1 = 8;
  CustomerReference refRelationship2 = 9;
  string uidEkyc = 10;
  string ekycRequestId = 11;
  string refAccountId = 12;
  string cif = 13;
  optional double lat = 14;
  optional double lng = 15;
  optional string incomeCode = 16;
}

message CreateApplicationResponse {
  lpms_enum.ELpmsResponseStatus status = 1;
  CreateApplicationDataResponse data = 2;
}

message CreateApplicationDataResponse { string applicationId = 1; }