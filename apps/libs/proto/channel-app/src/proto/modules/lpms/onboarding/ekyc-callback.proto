syntax = "proto3";

package lpms_ekyc_callback;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message OnboardingEKYCCallbackRequest {
  lpms_enum.ELpmsResponseStatus status = 1;
  OnboardingEKYCCallbackDataRequest data = 2;
}

message OnboardingEKYCCallbackDataRequest {
  optional string expiredIn = 1;
  optional lpms_reject_code.RejectCode rejectCode = 2;
  string onboardingId = 3;
  string phone = 4;
  optional string uidEkyc = 5;
  optional string jarvisId = 6;
  repeated OcrInfo ocrInfos = 7; // optional and list
}

message AddressEntity {
  optional string province = 1;
  optional string provinceCode = 2;
  optional string district = 3;
  optional string districtCode = 4;
  optional string ward = 5;
  optional string wardCode = 6;
  optional string street = 7;
}

message OcrInfo {
  string nid = 1;
  string fullNameVN = 2;
  string dob = 3;
  string gender = 4;
  string oldNid = 5; // optional
  string doe = 6;
  string issueDate = 7;
  string issuePlaceVN = 8;
  int32 age = 9;
  string nationality = 10;
  repeated AddressEntity addressEntities = 11; // optional and list
}