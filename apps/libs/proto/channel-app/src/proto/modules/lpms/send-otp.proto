syntax = "proto3";

package lpms_send_otp;

import "modules/lpms/reject-code.proto";

message SendOTPDto{
   string phone = 1;
   string channel = 2;
   string partner = 3;
}

message SendOTPResponse {
   bool success = 1;
   SendOTPDataResponse data = 2;
 }
 
 message SendOTPDataResponse {
   string sessionId = 1;
   string requestId = 2;
   string code = 3;
   string message = 4;
   optional  int32 expiresIn = 5; 
   optional int32 remain = 6;    
   bool isLocked = 7;
   optional lpms_reject_code.RejectCode rejectCode = 8;
   optional string expiredIn = 9;
 }