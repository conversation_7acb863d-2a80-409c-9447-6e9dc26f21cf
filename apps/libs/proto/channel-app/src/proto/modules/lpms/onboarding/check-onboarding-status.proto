syntax = "proto3";

package lpms_check_onboarding_status;

import "modules/contract/contract-info.proto";

message CheckOnboardingStatusRequest {
  int32 customerId = 1;
  string applicationId = 2;
}

enum ApplicationStatus {
  PROCESSING = 0;
  APPROVED = 1;
  REJECTED = 2;
  CANCELLED = 3;
  SIGNING = 4;
  SIGNED = 5;
}

enum FiLicense {
  Y = 0;
  N = 1;
}

message OnboardingStatusData {
  ApplicationStatus status = 1;
  optional string totalLimit = 2;
  optional string lastDayCanSign = 5;
  optional string providerCif = 6; // Cif from other provider, e.g. VPBank
  optional contract_info.ContractInfo contractInfo = 7;
  string applicationId = 8;
}

message CheckOnboardingStatusResponse {
  bool success = 1;
  OnboardingStatusData data = 2;
}