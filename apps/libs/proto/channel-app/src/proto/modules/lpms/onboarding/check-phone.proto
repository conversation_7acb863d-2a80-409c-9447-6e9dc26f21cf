syntax = "proto3";

package lpms_check_phone;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message CheckPhoneRequest {
    string phone = 1;
}

message CheckPhoneResponse {
  lpms_enum.ELpmsResponseStatus status = 1;
  optional CheckPhoneDataResponse data = 2;
}

message CheckPhoneDataResponse {
    optional string expiredIn = 1;
    optional lpms_reject_code.RejectCode rejectCode = 2;
}