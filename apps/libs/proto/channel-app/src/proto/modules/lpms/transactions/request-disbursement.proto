syntax = "proto3";

package lpms_request_disbursement;

import "modules/lpms/lpms-enum.proto";
import "modules/lpms/reject-code.proto";

message RequestDisbursementRequest {
  string applicationId = 1; // ID of the application
  int32 tenure = 2;         // Tenure in months
  optional string promotionCode = 3;
  optional string requestTime = 46; // ISO8601 formatted request time
}

message RequestDisbursementResponse {
  lpms_enum.ELpmsResponseStatus status = 1;
  RequestDisbursementDataResponse data = 2;
}

message RequestDisbursementDataResponse {
  optional string expiredIn = 1;
  optional lpms_reject_code.RejectCode rejectCode = 2;
  string applicationId = 3;
}
