syntax = "proto3";

package lpms_handle_transaction_reversal;

import "modules/lpms/lpms-enum.proto";

message HandleTransactionReversalRequest {
  string subApplicationId = 1;
  ETransactionReversalStatus status = 2;

  enum ETransactionReversalStatus {
    REFUNDED = 0;
    CANCELLED = 1;
    PARTIAL_REFUNDED = 2;
  }
}

message HandleTransactionReversalResponse {
  bool success = 1;
  HandleTransactionReversalDataResponse data = 2;
}

message HandleTransactionReversalDataResponse {
  string message = 1;
}


