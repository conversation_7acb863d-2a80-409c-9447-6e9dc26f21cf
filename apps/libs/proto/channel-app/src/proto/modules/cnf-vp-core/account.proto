syntax = "proto3";
package cnf_vp_core_account;

import "google/protobuf/struct.proto";

message GetAccountInfoRpcRequest { 
  oneof params {
    string cif = 1; // VP CIF
    string lcfCif = 2; // LCF CIF
    string contractNumber = 3; // Contract Number
  }
 }

message GetAccountInfoRpcResponse { 
    string casaAccountNumber = 1; 
    double availableLimit = 2;
    optional double remainingUsageLimit = 3; // LCF remaining usage limit for this month
    optional double usageLimit = 4; // LCF maximum usage limit for this month
    string status = 5;
}

message CheckExistingAccountRequest { string phone = 1; }

message CheckExistingAccountResponse { bool isExisted = 1; }

enum CreditAccountRpcStatus {
  ACC_OK = 0;
  ACC_LOCKED = 1;
}

message ChangeAccountStatusRequest {
  string cif = 1; // LCF CIF
  CreditAccountRpcStatus status = 2;
}

message ChangeAccountStatusResponse { CreditAccountRpcStatus status = 1; }

message GetCreditInfoRpcRequest {
  oneof params {
    string cif = 1; // VP CIF
    string lcfCif = 2; // LCF CIF
    string contractNumber = 3; // Contract Number
  }
  optional bool includeAllStauses = 4; // For portal internal check
}

message GetCreditInfoRpcResponse {
  double availableLimit = 1; // VP available limit
  double ovdAmount = 2;
  string status = 3;
  optional double usageLimit = 4; // LCF usage limit
  int32 paybackTimes = 5;
  optional double remainingUsageLimit  = 6; // LCF remaining usage limit
  repeated google.protobuf.Struct creditMatrix = 7; // LCF credit matrix
}

message InquiryCustomerRpcRequest {
  string phone = 1;
}

message InquiryCustomerRpcResponse {
  bool status = 1;
  string code = 2;
  string message = 3;
  optional InquiryCustomerData customer = 4;
  message InquiryCustomerData {
    string cif = 1;
    string fullName = 2;
    string email = 3;
    string phoneNo = 4;
    string livingAddress = 5;
  }
}