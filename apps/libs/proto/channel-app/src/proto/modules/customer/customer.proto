syntax = "proto3";
package customer;

message GetCustomerListRequest {
  int32 take = 1;
  int32 cursor = 2;
  bool first = 3;
  optional GetCustomerListRequestFilterBy filterBy = 4;
}

message GetCustomerInfoRequest {
  int32 id = 1;
}

message CustomerInfoResponse {
  int32 id = 1;
  string phone = 2;
  string email = 3;
  optional string fullName = 4;
  optional int32 creditLimit = 5;
  int32 customerId = 6;
  int32 userId = 7;
}

message PaginatedResult {
  repeated Data data = 1;
  Meta meta = 2;
}

message Meta {
  int32 total = 1;
  int32 take = 2;
  int32 cursor = 3;
}

message Data {
  int32 id = 1;
  string phone = 2;
  string email = 3;
}

message GetCustomerIdByCifRequest {
  string cif = 1;
}

message GetCustomerIdByCifResponse {
  optional int32 customerId = 1;
}

message InquiryCustomerInfoRequest {
  repeated int32 ids = 1;
  optional string name = 2;
}

message GetCustomerListRequestFilterBy {
  repeated int32 inIds = 1;
  repeated string inPhones = 2;
  optional bool isDeleted = 3;
}