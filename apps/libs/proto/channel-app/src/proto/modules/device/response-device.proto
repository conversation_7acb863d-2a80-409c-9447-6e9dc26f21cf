syntax = "proto3";
package device;

message DeviceTokenResponse {
  int32 id = 1;
  string token = 2;
  optional int32 customerId = 3;
  optional string cif = 4;
  optional string platform = 5;
  optional string createdAt = 6;
  optional string updatedAt = 7;
}

message InsertDeviceTokenResponse {
  int32 id = 1;
  string token = 2;
  optional int32 customerId = 3;
  optional string cif = 4;
  optional string platform = 5;
  optional string createdAt = 6;
}
message DeleteDeviceResponse {
  bool success = 1; 
}

message GetDevicePaginationResponse {
    repeated DeviceTokenResponse items = 1;
    int32 pageSize = 2;
    int32 page = 3;
    int32 total = 4;
}

message StoreDeviceInformationResponse {
  string deviceId = 1;
  optional int32 customerId = 2;
  optional string mobile = 3;
  optional string os = 4;
  optional string model = 5;
  optional string platform = 6;
  optional string createdAt = 7;
  optional string updatedAt = 8;
}

message GetDeviceInformationResponse {
  string deviceId = 1;
  optional int32 customerId = 2;
  optional string mobile = 3;
  optional string os = 4;
  optional string model = 5;
  optional string platform = 6;
  optional string createdAt = 7;
  optional string updatedAt = 8;
}