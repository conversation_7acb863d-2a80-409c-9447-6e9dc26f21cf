syntax = "proto3";
package device;
import "modules/device/device.proto";
import "enum.proto";

message InsertDeviceTokenRequest {
  string token = 1;
  optional int32 customerId = 2;
  optional string cif = 3;
  optional DevicePlatform platform = 4;
}

enum DevicePlatform{
  ANDROID = 0;
  IOS = 1;
}

message DeleteDeviceByIdRequest {
  int32 id = 1;
}

message DeleteDeviceByIdsRequest {
  repeated  int32 ids = 1;
}
message deleteDeviceByCustomerIdRequest{
  int32 customerId = 1;
 
}

message DeleteAllDeviceRequest{}

message GetDevicePaginationRequest {
  optional int32 pageSize = 1;
  optional int32 page = 2;
  optional OrderBy orderBy = 3;
  optional int32 customerId = 4;
  optional string platform = 5;
  optional string createdAt = 6;
  optional string updatedAt= 7;
}

message GetDeviceByCustomerId {
  int32 customerId = 1;
  optional string deviceId = 2;
}

message StoreDeviceInformationRequest {
  string deviceId = 1;
  string mobile = 2;
  optional int32 customerId = 3;
  optional DevicePlatform platform = 4;
  optional string model = 5;
  optional string os = 6;
}

message GetDeviceInformationRequest {
  string deviceId = 1;
  optional int32 customerId = 2;
  optional DevicePlatform platform = 3;
  optional string mobile = 4;
}