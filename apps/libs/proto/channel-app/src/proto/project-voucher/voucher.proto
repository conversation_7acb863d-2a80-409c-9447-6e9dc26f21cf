syntax = "proto3";
package voucher;

message SingleVoucherResult {
    bool success = 1;
    string message = 2;
    string payload = 3;
}

message Query {
    string projectId = 1;
    int32 take = 2;
    int32 skip = 3;
    string where = 4;
    string orderBy = 5;
}

message ValidateVouchersInput {
    string projectId = 1;
    string storeId = 2;
    repeated string codes= 3;
    string billNumber = 4;
    string provider = 5;
    string pin = 6;
}

message ReserveVouchersInput {
    string projectId = 1;
    string storeId = 2;
    repeated string codes= 3;
    string billNumber = 4;
    string billCreatedAt = 5;
    string provider = 6;
    string pin = 7;
}


message UseVouchersInput {
    string projectId = 1;
    string storeId = 2;
    repeated string codes= 3;
    string billNumber = 4;
    int32 totalBill = 5;
    string provider = 6;
    string pin = 7;
}

service VoucherMicroservice {
    rpc validateVouchers(ValidateVouchersInput) returns(SingleVoucherResult) {} 
    rpc reserveVouchers(ReserveVouchersInput) returns(SingleVoucherResult) {} 
    rpc unreserveVouchers(ReserveVouchersInput) returns(SingleVoucherResult) {} 
    rpc useVouchers(UseVouchersInput) returns(SingleVoucherResult) {} 
    rpc getVouchersLogs(Query) returns(SingleVoucherResult) {}
}