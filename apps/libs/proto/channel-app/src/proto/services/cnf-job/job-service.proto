syntax = "proto3";
package cnf_job;
import "modules/cnf-job/job.proto";

service CNF_JOB_JobsService {
    rpc CreateOrUpdateJob(cnf_job.CreateOrUpdateJobRequest) returns (cnf_job.CreateOrUpdateJobResponse);
    rpc GetJobs(cnf_job.GetJobsRequest) returns (cnf_job.GetJobsResponse);
    rpc handleJobStatus(cnf_job.JobStatusRequest) returns (cnf_job.JobStatusResponse);
    rpc CreateJob(cnf_job.CreateJobRequest) returns (cnf_job.CreateJobResponse);
    rpc UpdateJob(cnf_job.UpdateJobRequest) returns (cnf_job.UpdateJobResponse);
    rpc GetJobById(cnf_job.GetJobByIdRequest) returns (cnf_job.GetJobByIdResponse);
    rpc RunJobManually(cnf_job.RunJobManuallyRequest) returns (cnf_job.RunJobManuallyResponse);
}