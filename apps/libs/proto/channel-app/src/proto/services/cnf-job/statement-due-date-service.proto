syntax = "proto3";
package cnf_job;
import "modules/cnf-job/statement-due-date.proto";

service CNF_JOB_StatementDueDateService {
    rpc GetStatementDueDates(cnf_job.GetStatementDueDatesRequest) returns (cnf_job.GetStatementDueDatesResponse);
    rpc UpdateStatementDueDateById(cnf_job.UpdateStatementDueDateByIdRequest) returns (cnf_job.UpdateStatementDueDateByIdResponse);
    rpc CreateMultipleStatementDueDates(cnf_job.CreateMultipleStatementDueDatesRequest) returns (cnf_job.CreateMultipleStatementDueDatesResponse);
    rpc GetStatementDueDate(cnf_job.GetStatementDueDateRequest) returns (cnf_job.GetStatementDueDateResponse);
}
