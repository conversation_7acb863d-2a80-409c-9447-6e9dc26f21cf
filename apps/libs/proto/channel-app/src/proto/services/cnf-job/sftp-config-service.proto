syntax = "proto3";
package cnf_job;
import "modules/cnf-job/sftp-config.proto";

service CNF_JOB_SftpConfigService {
    rpc UpdateSftpConfigById(cnf_job.UpdateSftpConfigByIdRequest) returns (cnf_job.UpdateSftpConfigByIdResponse);
    rpc GetSftpConfigs(cnf_job.GetSftpConfigsRequest) returns (cnf_job.GetSftpConfigsResponse);
    rpc GetSftpConfigByName(cnf_job.GetSftpConfigByNameRequest) returns (cnf_job.GetSftpConfigByNameResponse);
}