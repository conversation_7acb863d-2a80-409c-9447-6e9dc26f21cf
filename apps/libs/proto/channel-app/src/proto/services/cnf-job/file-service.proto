syntax = "proto3";
package cnf_job;
import "modules/cnf-job/file.proto";

service CNF_JOB_FileService {
    rpc DownloadFile(cnf_job.DownloadFileRequest) returns (stream cnf_job.DownloadFileResponse);
    rpc GetFileLogs(cnf_job.GetFileLogsRequest) returns (cnf_job.GetFileLogsResponse);
    rpc ManualPullFile(cnf_job.ManualPullFileRequest) returns (cnf_job.ManualPullFileResponse);
    rpc ManualPushFile(cnf_job.ManualPushFileRequest) returns (cnf_job.ManualPushFileResponse);
    rpc GetReportFileLogs(cnf_job.GetReportFileLogsRequest) returns (cnf_job.GetReportFileLogsResponse);
}
