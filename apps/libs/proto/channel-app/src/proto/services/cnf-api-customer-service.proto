syntax = "proto3";
package customer;
import "modules/customer/customer.proto";
import "modules/contract-portion/contract-portion.proto";
service CNF_API_CustomerService {
  rpc getCustomerList(GetCustomerListRequest) returns (PaginatedResult);
  rpc getCustomerInfoById(GetCustomerInfoRequest) returns (CustomerInfoResponse);
  rpc getCustomerIdByCif(GetCustomerIdByCifRequest) returns (GetCustomerIdByCifResponse);
  rpc getContractPortion(contract_portion.contractPortionRequest) returns (contract_portion.contractPortionResponse) {}
  rpc inquiryCustomers(InquiryCustomerInfoRequest) returns (stream CustomerInfoResponse);
}
