syntax = "proto3";
package notification_broadcast;

// Service definition
service NotificationBroadcastService {
  rpc findOne(FindOneNotificationBroadcastRequest) returns (NotificationBroadcastResponse);
  rpc pagination(GetNotificationBroadcastPaginationRequest) returns (GetNotificationBroadcastPaginationResponse);
  rpc insert(InsertNotificationBroadcastRequest) returns (NotificationBroadcastResponse);
  rpc update(UpdateNotificationBroadcastRequest) returns (NotificationBroadcastResponse);
  rpc delete(DeleteNotificationBroadcastRequest) returns (NotificationBroadcastResponse);
  rpc publish(PublishNotificationBroadcastRequest) returns (GetStatusPublishNotificationBroadcastResponse);
  rpc export(GetNotificationBroadcastPaginationRequest) returns (stream ExportNotificationBroadcastResponse);
}

//request
message InsertNotificationBroadcastRequest {
  optional string content = 1;
  optional string title = 2;
  optional string type = 3;
  optional string actionUrl = 4;
  optional string params = 5;
  optional string primaryButton = 6;
  optional bool isActive = 7;
  optional string stores = 8;
  optional int32 createdBy = 9;
  int32 statusBroadcast = 10;
  optional string images = 11;
  optional string scheduleAt = 12;
  repeated NotificationBroadcastTargetInfo targets = 13;
  repeated string channels = 14;
}

message UpdateNotificationBroadcastRequest {
  int32 broadcastId = 1;
  optional string content = 2;
  optional string title = 3;
  optional string type = 4;
  optional string actionUrl = 5;
  optional string params = 6;
  optional string primaryButton = 7;
  optional bool isActive = 8;
  optional string stores = 9;
  optional int32 updatedBy = 10;
  optional string images = 11;
  optional string scheduleAt = 12;
  repeated NotificationBroadcastTargetInfo targets = 13;
  repeated string channels = 14;
}

message FindOneNotificationBroadcastRequest {
   int32 broadcastId = 1;
}

message PublishNotificationBroadcastRequest {
   int32 broadcastId = 1;
}

message GetNotificationBroadcastPaginationRequest {
   optional int32 pageSize = 1;
   optional int32 page = 2;
   optional string orderBy = 3;
   optional string statusBroadcast = 4;
   optional string createdAt = 5;
   optional string scheduleAt = 6;
   optional string title = 7;
}

message DeleteNotificationBroadcastRequest {
   optional int32 broadcastId = 1;
}

//reponse

message NotificationBroadcastResponse {
  int32 broadcastId = 1;
  optional string content = 2;
  optional string title = 3;
  optional string targetType = 4;
  optional string type = 5;
  optional string actionUrl = 6;
  optional string params = 7;
  optional string primaryButton = 8;
  optional bool isActive = 9;
  optional string stores = 10;
  optional int32 createdBy = 11;
  optional int32 updatedBy = 12;
  optional string statusBroadcast = 13;
  optional string targetIds = 14;
  optional string images = 15;
  optional string createdAt = 16;
  optional string scheduleAt = 17;
  repeated string channels = 18;
  string rawContent = 19;
  optional int32 noOfNotifications = 20;
}

message GetNotificationBroadcastPaginationResponse {
    repeated NotificationBroadcastResponse items = 1;
    int32 pageSize = 2;
    int32 page = 3;
    int32 total = 4;
}

message GetStatusPublishNotificationBroadcastResponse {
    int32 code = 1;
    string message = 2;
}

//enum
enum OrderByValue {
  ASC = 0;
  DESC = 1;
}

message NotificationBroadcastTargetInfo {
  string targetId = 1;
  string type = 2;
}

message ExportNotificationBroadcastResponse {
  bytes content = 1;
}