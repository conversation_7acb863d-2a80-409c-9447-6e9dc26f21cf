syntax = "proto3";
package tracking;
import "modules/tracking/gps/request.proto";
import "modules/tracking/gps/response.proto";

// Service definition
service TrackingGrpcAPIService {
  rpc storeGPSLocation(StoreGPSLocationRequest) returns (GPSLocationResponse);
  rpc getGPSLocationById(GetGPSLocationByIdRequest) returns (GPSLocationResponse);
  rpc inquiryGPSLocations(InquiryGPSLocationsRequest) returns (stream GPSLocationResponse);
  rpc syncGPSLocations(SyncGPSLocationsRequest) returns (SyncGPSLocationsResponse);
  rpc pagination(GPSLocationsPaginationRequest) returns (PaginationGPSLocationResponse);
}