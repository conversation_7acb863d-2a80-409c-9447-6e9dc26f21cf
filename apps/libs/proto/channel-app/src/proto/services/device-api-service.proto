syntax = "proto3";
package device;
import "modules/device/request-device.proto";
import "modules/device/response-device.proto";

// Service definition
service DeviceService {
  rpc insertDevice(InsertDeviceTokenRequest) returns (InsertDeviceTokenResponse);
  rpc deleteById(DeleteDeviceByIdRequest) returns (DeleteDeviceResponse);
  rpc deleteByIds(DeleteDeviceByIdsRequest) returns (DeleteDeviceResponse);
  rpc deleteAllDevices(DeleteAllDeviceRequest) returns (DeleteDeviceResponse);
  rpc deleteByCustomerId(deleteDeviceByCustomerIdRequest) returns(DeleteDeviceResponse);
  rpc pagination(GetDevicePaginationRequest) returns (GetDevicePaginationResponse);
  rpc getDeviceByCustomerId(GetDeviceByCustomerId) returns (DeviceTokenResponse);
  rpc storeDeviceInfo(StoreDeviceInformationRequest) returns (StoreDeviceInformationResponse);
  rpc getDeviceInfo(GetDeviceInformationRequest) returns (GetDeviceInformationResponse);
}