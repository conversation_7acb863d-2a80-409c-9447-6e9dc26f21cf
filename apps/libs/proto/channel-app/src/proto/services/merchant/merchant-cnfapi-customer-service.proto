syntax = "proto3";
package cnfapi_customer;
import "modules/merchant/get-customer-list.proto";
import "modules/merchant/get-customer-detail.proto";
import "modules/merchant/get-customer-main-contract.proto";
import "modules/merchant/customer-detail/customer-note.proto";
import "modules/merchant/credit-account.proto";
import "modules/merchant/customer-note.proto";
import "modules/merchant/customer-registered-history.proto";
import "modules/merchant/customer-available-limit.proto";
import "modules/merchant/customer-billing.proto";
import "google/protobuf/empty.proto";
import "modules/cnf-vp-core/repayment.proto";
import "modules/merchant/onboard-source/get-onboard-source-data-log.proto";
import "modules/merchant/onboard-source/create-new-onboard-source-data.proto";
import "modules/merchant/onboard-source/delete-onboard-source-data.proto";
import "modules/merchant/get-otp.proto";

service MerchantAPI_CNFAPI_CustomerService {
  rpc getCustomers(customer.GetCustomersPayload)
      returns (customer.GetCustomersResponse) {}
  rpc getCustomerDetail(customer_detail.GetCustomerDetailPayload)
      returns (customer_detail.GetCustomerDetailResponse) {}
  rpc updateStatusCustomer(customer_detail.UpdateStatusCustomer)
      returns (google.protobuf.Empty) {}
  rpc getMainContractPdf(customer_main_contract.GetCustomerMainContractRequest)
      returns (customer_main_contract.GetCustomerMainContractResponse) {}
  rpc createCustomerNote(customer_note.CreateCustomerNoteRequest)
      returns (customer_note.CreateCustomerNoteResponse) {}
  rpc changeCreditAccountStatus(credit_account.ChangeCreditAccountStatusRequest)
      returns (credit_account.ChangeCreditAccountStatusResponse) {}
  rpc getCustomerNotes(customer_note.GetCustomerNoteRequest)
      returns (customer_note.GetCustomerNoteResponse) {}
  rpc getCustomerRegisteredHistories(
      customer_registered_history.GetCustomerRegisteredHistoryRequest)
      returns (
          customer_registered_history.GetCustomerRegisteredHistoryResponse) {}
  rpc getCreditAccount(credit_account.GetCreditAccountRequest)
      returns (credit_account.GetCreditAccountResponse) {}
  rpc getAvailableLimit(
      customer_available_limit.GetCustomerAvailableLimitRequest)
      returns (customer_available_limit.GetCustomerAvailableLimitResponse) {}

  // billing
  rpc getCustomerBillHistory(customer_billing.GetCustomerBillHistoryRequest)
      returns (vp_core_repayment.GetBillHistoryResponseData) {}
  rpc getCustomerBillDetail(customer_billing.GetCustomerBillDetailRequest)
      returns (customer_billing.GetCustomerBillDetailResponse) {}

  // account
  rpc deleteCustomerAccount(customer.DeleteAccountRpcRequestPayload)
      returns (google.protobuf.Empty) {}

  //   onboard source
  rpc getOnboardSourceDataLogList(
      onboard_source.GetOnboardSourceDataLogListRequest)
      returns (onboard_source.GetOnboardSourceDataLogListResponse) {}
  rpc createNewOnboardSourceData(
      onboard_source.CreateNewOnboardSourceDataRequest)
      returns (onboard_source.CreateNewOnboardSourceDataResponse) {}
  rpc deleteOnboardSourceData(onboard_source.DeleteOnboardSourceDataRequest)
      returns (onboard_source.DeleteOnboardSourceDataResponse) {}

  // otp
  rpc sendOtp(cnf_otp.SendOtpRequest) returns (cnf_otp.SendOtpResponse) {}
  rpc verifyOtp(cnf_otp.VerifyOtpRequest) returns (cnf_otp.VerifyOtpResponse) {}
}