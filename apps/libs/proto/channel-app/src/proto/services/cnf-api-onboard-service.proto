syntax = "proto3";
package cnf_api_onboard;

import "google/protobuf/empty.proto";

import "modules/lpms/onboarding/ekyc-callback.proto";
import "modules/lpms/onboarding/evaluation-process-callback.proto";
import "modules/lpms/onboarding/sign-contract-result-callback.proto";

service CNF_API_OnboardService {
  rpc finalContractCallback(
      lpms_sign_contract_result_callback.SignContractResultCallbackRequest)
      returns (google.protobuf.Empty);
  rpc evaluationCallback(lpms_evaluation_process_callback.OnboardingEvaluationProcessCallbackRequest)
      returns (google.protobuf.Empty);
  rpc ekycCallback(lpms_ekyc_callback.OnboardingEKYCCallbackRequest)
      returns (google.protobuf.Empty);
}