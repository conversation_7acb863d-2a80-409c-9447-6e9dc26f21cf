syntax = "proto3";
package lpms;

import "google/protobuf/empty.proto";

import "modules/lpms/send-otp.proto";
import "modules/lpms/verify-otp.proto";
import "modules/lpms/onboarding/check-phone.proto";
import "modules/lpms/onboarding/check-identity-card.proto";
import "modules/lpms/onboarding/get-identity-card-img.proto";
import "modules/lpms/onboarding/check-card-id-rules.proto";
import "modules/lpms/sign-contract.proto";
import "modules/lpms/onboarding/evaluation-process.proto";

import "modules/lpms/onboarding/check-onboarding-status.proto";
import "modules/lpms/onboarding/get-customer-contract.proto";

import "modules/lpms/transactions/make-transaction.proto";
import "modules/lpms/transactions/request-disbursement.proto";
import "modules/lpms/transactions/validate-promotion.proto";
import "modules/lpms/transactions/reversal-transaction.proto";
import "modules/lpms/transactions/disbursement-confirmation.proto";

import "modules/lpms/product-configs/get-jobs.proto";
import "modules/lpms/product-configs/get-provinces.proto";
import "modules/lpms/product-configs/get-districts.proto";
import "modules/lpms/product-configs/get-wards.proto";
import "modules/lpms/product-configs/get-incomes.proto";

// Service definition
service LpmsService {
  // product-configs
  rpc getJobs(google.protobuf.Empty) returns (lpms_get_jobs.GetJobsResponse);
  rpc getProvinces(google.protobuf.Empty)
      returns (lpms_get_provinces.GetProvincesResponse);
  rpc getDistricts(google.protobuf.Empty)
      returns (lpms_get_districts.GetDistrictsResponse);
  rpc getWards(google.protobuf.Empty) returns (lpms_get_wards.GetWardsResponse);
  rpc getIncomes(google.protobuf.Empty)
      returns (lpms_get_incomes.GetIncomesResponse);

  // otp
  rpc send(lpms_send_otp.SendOTPDto) returns (lpms_send_otp.SendOTPResponse) {};
  rpc verify(lpms_verify_otp.VerifyOTPDto)
      returns (lpms_verify_otp.VerifyOtpResponse) {};

  // onboarding
  rpc checkPhone(lpms_check_phone.CheckPhoneRequest)
      returns (lpms_check_phone.CheckPhoneResponse);
  rpc checkIdentityCard(lpms_check_identity_card.CheckIdentityCardRequest)
      returns (lpms_check_identity_card.CheckIdentityCardResponse);
  rpc checkCardIdRules(lpms_check_id_rules.CheckCardIdRulesRequest)
      returns (lpms_check_id_rules.CheckCardIdRulesResponse);
  rpc onboardingEvaluationProcess(
      lpms_evaluation_process.OnboardingEvaluationProcessRequest)
      returns (lpms_evaluation_process.CreateApplicationResponse);
  rpc checkOnboardingStatus(
      lpms_check_onboarding_status.CheckOnboardingStatusRequest)
      returns (lpms_check_onboarding_status.CheckOnboardingStatusResponse);
  rpc getIdentityCardImg(lpms_get_identity_card_img.GetIdentityCardImgRequest)
      returns (lpms_get_identity_card_img.GetIdentityCardImgResponse);

  // contract
  rpc getCustomerContract(lpms_get_customer_contract.GetCustomerContractRequest)
      returns (lpms_get_customer_contract.GetCustomerContractResponse);
  rpc generateSignContractOTP(lpms_sign_contract.GenerateSignContractOtpRequest)
      returns (lpms_sign_contract.GenerateSignContractOtpResponse);
  rpc signContract(lpms_sign_contract.SignContractRequest)
      returns (lpms_sign_contract.SignContractResponse);

  // make-transaction
  rpc makeTransaction(lpms_make_transaction.MakeTransactionRequest)
      returns (lpms_make_transaction.MakeTransactionResponse);
  rpc validatePromotion(lpms_validate_promotion.ValidatePromotionRequest)
      returns (lpms_validate_promotion.ValidatePromotionResponse);
  rpc requestDisbursement(lpms_request_disbursement.RequestDisbursementRequest)
      returns (lpms_request_disbursement.RequestDisbursementResponse);
  rpc disbursementConfirmation(lpms_disbursement_confirmation.DisbursementConfirmationRequest)
      returns (lpms_disbursement_confirmation.DisbursementConfirmationResponse);
  rpc handleTransactionReversal(
      lpms_handle_transaction_reversal.HandleTransactionReversalRequest)
      returns (
          lpms_handle_transaction_reversal.HandleTransactionReversalResponse);
}