syntax = "proto3";
package notification;
import "modules/notification/request-notification.proto";
import "modules/notification/response-notification.proto";

// Service definition
service NotificationService {
  rpc findOne(notification.FindOneNotificationRequest) returns (notification.NotificationResponse);
  rpc pagination(notification.GetNotificationPaginationRequest) returns (notification.GetNotificationPaginationResponse);
  rpc insert(notification.InsertNotificationRequest) returns (notification.NotificationResponse);
  rpc update(notification.UpdateNotificationRequest) returns (notification.NotificationResponse);
  rpc delete(notification.DeleteNotificationRequest) returns (notification.NotificationResponse);
  rpc getByCustomerId(notification.GetByCustomerNotificationRequest) returns (notification.GetNotificationPaginationResponse);
  rpc exportNotifications(notification.GetNotificationPaginationRequest) returns (stream notification.ExportNotificationResponse);

  //customer notification
  rpc getNotificationByUuid(notification.GetNotificationByUUIDRequest) returns (notification.NotificationResponse);
  rpc customerDeleteNotification(notification.CustomerDeleteNotificationRequest) returns (notification.CustomerDeleteNotificationResponse);
  rpc updateSentNotification(notification.UpdateSentNotificationRequest) returns (notification.NotificationResponse);
  rpc getListNotificationByType (notification.GetListNotificationByTypeRequest) returns (notification.GetNotificationPaginationResponseCursor);
  rpc getNotificationStats (notification.GetNotificationStatsRequest) returns (notification.GetNotificationStatsResponse);
}
