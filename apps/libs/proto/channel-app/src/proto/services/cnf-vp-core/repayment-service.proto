syntax = "proto3";
package cnf_vp_core;

import "modules/cnf-vp-core/repayment.proto";

service CNF_VP_CORE_RepaymentService {
  rpc inquiryRepaymentInfo(vp_core_repayment.InquiryRepaymentInfoRequest)
      returns (vp_core_repayment.InquiryRepaymentInfoResponse);
  rpc generateRepaymentQRCode(vp_core_repayment.GenerateRepaymentQRCodeRequest)
      returns (vp_core_repayment.GenerateRepaymentQRCodeResponse);
  rpc getBillDetail(vp_core_repayment.GetBillDetailRequest)
      returns (vp_core_repayment.GetBillDetailResponse);
  rpc getBillHistory(vp_core_repayment.GetBillHistoryRequest)
      returns (vp_core_repayment.GetBillHistoryResponse);
}
