syntax = "proto3";
package cnf_vp_core;

import "modules/cnf-vp-core/account.proto";

service CNF_VP_CORE_AccountService {
  rpc getAccountInfo(cnf_vp_core_account.GetAccountInfoRpcRequest)
      returns (cnf_vp_core_account.GetAccountInfoRpcResponse);
  rpc checkExistedAccount(cnf_vp_core_account.CheckExistingAccountRequest)
      returns (cnf_vp_core_account.CheckExistingAccountResponse);
  rpc changeAccountStatus(cnf_vp_core_account.ChangeAccountStatusRequest)
      returns (cnf_vp_core_account.ChangeAccountStatusResponse);
  rpc getCreditInfo(cnf_vp_core_account.GetCreditInfoRpcRequest) returns (cnf_vp_core_account.GetCreditInfoRpcResponse);
  rpc inquiryCustomer(cnf_vp_core_account.InquiryCustomerRpcRequest) returns (cnf_vp_core_account.InquiryCustomerRpcResponse);
}