syntax = "proto3";
package cnf_vp_core;

import "modules/cnf-vp-core/gateway.proto";

service CNF_VP_CORE_GatewayService {
  rpc call(vp_core_gateway.VPHttpCallRequest) returns (vp_core_gateway.VPHttpCallResponse);
  rpc callVan(vp_core_gateway.VPVanHttpCallRequest) returns (vp_core_gateway.VPHttpCallResponse);
  rpc callMSA(vp_core_gateway.VPMSAHttpCallRequest) returns (vp_core_gateway.VPHttpCallResponse);
}
