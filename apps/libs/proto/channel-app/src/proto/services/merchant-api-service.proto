syntax = "proto3";
package order;
import "modules/transaction/transaction.proto";
import "modules/payment-transaction/request-payment.proto";
import "modules/payment-transaction/get-transaction-by-code.proto";
import "modules/refund-transaction/refund-transaction.proto";
import "modules/refund-reversal/refund-reversal.proto";
import "modules/cancel-transaction/cancel-transaction.proto";
import "modules/settlement-transaction/settlement-transaction.proto";
import "modules/transaction/pagination-transaction.proto";
import "modules/request-refund-transaction/request-refund-transaction.proto";
import "google/protobuf/empty.proto";
import "modules/merchant/get-customer-detail.proto";
import "modules/reconcile-transaction/reconcile-transaction.proto";
import "modules/transaction/export-list-transaction.proto";
import "modules/transaction/transaction-confirmation.proto";
import "modules/transaction/trigger-report-file.proto";

service Merchant_API_OrderService {
    rpc getTransactionStatistic(transaction.GetTransactionStsPayload) returns(transaction.GetTransactionStsResponse) {}
    rpc getTransactionDetail(transaction.GetTransactionByIdPayload) returns(transaction.GetTransactionByIdResponse) {}
    rpc getTransaction (transaction.GetTransactionPayload) returns (transaction.GetTransactionResponse) {}
    rpc getTransactionByMerchantNumber (transaction.GetTransactionByMerchantNumberPayload) returns (transaction.GetTransactionByMerchantNumberResponse) {}
    rpc requestPayment (payment_transaction.PaymentRequestModel) returns (payment_transaction.PaymentRequestResponse) {}
    rpc getTransactionByCode (payment_transaction.GetTransactionByCodePayload) returns (payment_transaction.GetTransactionByCodeResponse) {}

    rpc cancelPayment (cancel_transaction.CancelPaymentRequestPayload) returns (cancel_transaction.CancelPaymentResponsePayload) {}

    //refund transaction
    rpc getRefundTransaction(refund_transaction.GetRefundTransactionPayload) returns (refund_transaction.GetRefundTransactionResponse) {}
    rpc getRefundTransactionDetail(refund_transaction.GetRefundTransactionDetailPayload) returns (refund_transaction.GetRefundTransactionDetailResponse) {}
    rpc getRefundTransactionByMerchantNumber(refund_transaction.GetRefundTransactionByMerchantNumberPayload) returns (refund_transaction.GetRefundTransactionResponse) {}
    rpc makeRefund(refund_reversal.RefundReversalPayload) returns (refund_reversal.RefundReversalResponse) {}
    rpc inquiryRefund(refund_reversal.InquiryRefundReversalPayload) returns (refund_reversal.InquiryRefundReversalResponse) {}

    //settlement transaction
    rpc getSettlementTransaction(settlement_transaction.GetSettlementTransactionPayload) returns (settlement_transaction.GetSettlementTransactionResponse) {}
    rpc getSettlementTransactionStatistic(settlement_transaction.GetSettlementTransactionStsPayload) returns(settlement_transaction.GetSettlementTransactionStsResponse) {}
    rpc importSettlementTransactionRec(settlement_transaction.FileUploadRequest) returns(settlement_transaction.FileUploadResponse) {}

    // Admin transaction
    rpc paginationTransaction(transaction.TransactionPaginationPayload) returns(transaction.TransactionPaginationResponse) {}
    rpc exportTransactions(transaction.ExportListTransactionPayload) returns(transaction.ExportListTransactionResponse) {}
    rpc triggerReportFile(transaction.TriggerReportFilePayload) returns(transaction.TriggerReportFileResponse) {}
    rpc getReportFileHistory(transaction.GetReportFileHistoryPayload) returns(transaction.GetReportFileHistoryResponse) {}
    rpc getReportFileUrl(transaction.GetReportFileUrlPayload) returns(transaction.GetReportFileUrlResponse) {}
    // request refund transaction

    rpc makeRequestRefund(request_refund_transaction.RequestRefundReversalPayload) returns(request_refund_transaction.RequestRefundReversalResponse) {}
    rpc getListRequestRefund(request_refund_transaction.GetListRequestRefundPayload) returns(request_refund_transaction.GetListRequestRefundResponse) {}
    rpc exportListRequestRefund(request_refund_transaction.ExportListRequestRefundPayload) returns(request_refund_transaction.ExportListRequestRefundResponse) {}
    rpc processRefundRequest(request_refund_transaction.ProcessRefundRequestPayload)  returns (refund_reversal.RefundReversalResponse) {}
    rpc processPreparingRefundRequest(request_refund_transaction.ProcessPreparingRefundRequestPayload)  returns (refund_reversal.RefundReversalResponse) {}
    rpc checkingFinalRefund(google.protobuf.Empty) returns (google.protobuf.Empty) {}
    rpc getRequestRefundDetail(request_refund_transaction.GetRequestRefundDetailPayload) returns (request_refund_transaction.GetRequestRefundDetailResponse) {}
    rpc getPaymentRefundTransactionByMerchantNumber(refund_transaction.GetRefundTransactionPayload) returns (refund_transaction.GetRefundTransactionResponse) {}
    rpc exportListRefundTransaction(refund_transaction.ExportListRefundTransactionPayload) returns ( refund_transaction.ExportListRefundTransactionResponse) {}

    rpc getTransactionDetailByCustomerId(customer_detail.GetCustomerDetailPayload) returns (customer_detail.GetCustomerDetailResponse){}

    rpc updateCycleTransaction(reconcile_transaction.UpdateCycleTransactionPayload) returns (reconcile_transaction.UpdateCycleTransactionResponse) {}
    rpc getReconcileTransaction(reconcile_transaction.GetTransactionPayload) returns (reconcile_transaction.GetTransactionResponse) {}
    rpc updateReconcileTransaction(reconcile_transaction.UpdateReconcileTransactionPayload) returns (reconcile_transaction.UpdateReconcileTransactionResponse) {}
    rpc getReconcileTransactionSts(reconcile_transaction.GetReconcileTransactionStsPayload) returns (reconcile_transaction.GetReconcileTransactionStsResponse) {}
    rpc getNextCycleTimeByMerchantCode(reconcile_transaction.GetNextCycleTimeByMerchantCodePayload) returns (reconcile_transaction.GetNextCycleTimeByMerchantCodeResponse) {}
    rpc executeReconcileTransactionTask(reconcile_transaction.ExecuteReconcileTransactionTaskPayload) returns (reconcile_transaction.ExecuteReconcileTransactionTaskResponse) {}

    // Admin Transaction Confirmation
    rpc getTransactionConfirmations(transaction_confirmation.GetTransactionConfirmationRequestPayload) returns (transaction_confirmation.GetTransactionConfirmationResponse) {}
    rpc resolveTransactionConfirmation(transaction_confirmation.ResolveTransactionConfirmationRequestPayload) returns (transaction_confirmation.ResolveTransactionConfirmationResponsePayload) {}
    rpc getTransactionConfirmationById(transaction_confirmation.GetTransactionConfirmationByIdRequestPayload) returns (transaction_confirmation.GetTransactionConfirmationByIdResponse) {}
}