syntax = "proto3";
package urlShortenerService;
import "modules/url-shortener/render.proto";
import "modules/url-shortener/short-url.proto";
import "modules/url-shortener/template.proto";
import "google/protobuf/empty.proto";
service UrlShortenerService {
  rpc render(urlShortener.RenderRequest) returns (urlShortener.RenderResponse) {}
  rpc createTemplate(template.RequestCreateTemplate) returns (google.protobuf.Empty) {}
  rpc createShortUrl(shortUrl.RequestCreateShortUrl) returns (shortUrl.ResponseCreateShortUrl) {}
  rpc updateTemplate(template.Template) returns (google.protobuf.Empty) {}
  rpc getTemplate(template.GetTemplatePayload) returns (template.TemplatePaginationResponse){}
  rpc generateShortUrl(shortUrl.RequestCreateShortUrl) returns (shortUrl.ResponseCreateShortUrl) {}
}