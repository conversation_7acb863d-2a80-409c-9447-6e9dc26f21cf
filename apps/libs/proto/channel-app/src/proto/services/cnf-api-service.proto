syntax = "proto3";
package order;
import "modules/payment-transaction/get-transaction-by-code.proto";
import "modules/payment-transaction/make-transaction.proto";
import "modules/transaction/get-process-transaction.proto";
import "modules/transaction/make-transaction-callback.proto";
import "google/protobuf/empty.proto";
import "modules/transaction/sign-sub-application-transaction.proto";
import "modules/transaction/get-customer-transaction.proto";
import "modules/transaction/sign-sub-application-callback.proto";
import "modules/transaction/get-transaction-by-sub-contract-id.proto";
import "modules/payment/payment-callback.proto";
import "modules/payment/submit-payment.proto";
import "modules/payment-portion/payment-portion.proto";
import "modules/payment-portion/payment-portion-detail.proto";
import "modules/transaction/status-sign-sub-application.proto";
import "modules/transaction/request-disbursement-callback.proto";
import "modules/payment/sign-sub-application.proto";
import "schema.proto";
import "modules/payment/validate-promotion.proto";
import "modules/transaction/pagination-transaction.proto";
import "modules/refund-transaction/refund-transaction.proto";
import "modules/transaction/get-bill-transactions.proto";
import "modules/transaction/total-transaction.proto";
import "modules/transaction/check-eligible-for-transaction.proto";

service CNF_API_OrderService {
  rpc getTransactionByCode(payment_transaction.GetTransactionByCodePayload)
      returns (payment_transaction.GetTransactionByCodeResponse) {}
  rpc makeTransaction(payment_transaction.MakeTransactionPayload)
      returns (payment_transaction.MakeTransactionResponse) {}
  rpc getProcessTransaction(transaction.GetProcessTransactionPayload)
      returns (transaction.GetProcessTransactionResponse) {}
  rpc makeTransactionCallback(transaction.MakeTransactionCallbackPayload)
      returns (google.protobuf.Empty) {}
  rpc signSubApplicationTransaction(
      transaction.SignSubApplicationTransactionPayload)
      returns (google.protobuf.Empty) {}
  rpc getCustomerTransaction(transaction.GetCustomerTransactionPayload)
      returns (transaction.GetCustomerTransactionResponse) {}
  rpc requestDisbursementCallback(
      transaction.RequestDisbursementCallbackPayload)
      returns (google.protobuf.Empty) {}
  rpc signSubApplicationCallback(transaction.SignSubApplicationCallbackPayload)
      returns (google.protobuf.Empty) {}
  rpc getTransactionBySubContractId(
      transaction.GetTransactionBySubContractIdPayload)
      returns (schema.Transaction) {}
  rpc paymentCallback(payment.PaymentCallbackRequest)
      returns (google.protobuf.Empty) {}
  rpc paymentPortions(payment_portion.PaymentPortionPayload)
      returns (payment_portion.PaymentPortionResponse) {}
  rpc submitPayment(payment.SubmitPaymentPayload)
      returns (payment.SubmitPaymentResponse) {}
  rpc statusSignSubApplication(transaction.StatusSignSubApplicationPayload)
      returns (transaction.StatusSignSubApplicationRepsonsePayload) {}
  rpc signSubApplication(payment.SignSubApplicationPayload)
      returns (google.protobuf.Empty) {}
  rpc portionPaymentDetails(payment_portion.PaymentPortionDetailPayload)
      returns (payment_portion.PaymentPortionDetailResponse) {}
  rpc validatePromotion(payment.ValidatePromotionPayload)
      returns (payment.ValidatePromotionResponse) {}
  rpc paginationTransaction(transaction.TransactionPaginationPayload)
      returns (transaction.TransactionPaginationResponse) {}
  rpc getRefundTransactionDetail(
      refund_transaction.GetRefundTransactionDetailPayload)
      returns (refund_transaction.GetRefundTransactionDetailResponse) {}
  rpc getBillTransactions(transaction.GetBillTransactionsPayload)
      returns (transaction.GetBillTransactionsResponse) {}
  rpc totalTransaction(transaction.TotalTransactionPayload)
      returns (transaction.TotalTransactionResponse) {}
  rpc checkEligibleForTransaction(
      transaction.CheckEligibleForTransactionPayload)
      returns (transaction.CheckEligibleForTransactionResponse) {}
}
