syntax = "proto3";
package payment_provider;

message Id {
    int32 id = 1;
}

message PaymentProvider {
    int32 id = 1;
    string projectId = 2;
    string partnerName = 3;
    string momoPartnerCode = 4;
    string momoAccessKey = 5;
    string momoSecretKey = 6;
    string momoPublicKey = 7;
    string zaloAppIdWeb = 8;
    string zaloSecretKeyWeb = 9;
    string zaloCallbackKeyWeb = 10;
    string zaloAppIdApp = 11;
    string zaloSecretKeyApp = 12;
    string zaloCallbackKeyApp = 13;
    string shopeeClientId = 14;
    string shopeeSecretKey = 15;
    string shopeeClientIdMobile = 16;
    string shopeeSecretKeyMobile = 17;
    string vnpayTerminalId = 18;
    string vnpaySecretKey = 19;
    string vnpayMerchantCode = 20;
    string vnpayMerchantName = 21;
    string vnpayMerchantType = 22;
    string vnpayRefundSecretKey = 23;
    string vnpayAppId = 24;
    string redirectUrl = 25;  
    repeated string updateOrderUrls = 26;
    repeated string updateTokenUrls = 27;
    repeated string updateApprovalCodeUrls = 28;
    string callbackLoginToken = 29;
    string description = 30;
    bool isActiveOrderGroupId = 31;
    bool isActive = 32;
    string momoIpnUrl = 33;
    string shortCompanyName = 34;
    bool passUserInfoToCreditCard = 35;
}

message CreatePaymentProviderDataInput {
    string projectId = 2;
    string partnerName = 3;
    string momoPartnerCode = 4;
    string momoAccessKey = 5;
    string momoSecretKey = 6;
    string momoPublicKey = 7;
    string zaloAppIdWeb = 8;
    string zaloSecretKeyWeb = 9;
    string zaloCallbackKeyWeb = 10;
    string zaloAppIdApp = 11;
    string zaloSecretKeyApp = 12;
    string zaloCallbackKeyApp = 13;
    string shopeeClientId = 14;
    string shopeeSecretKey = 15;
    string shopeeClientIdMobile = 16;
    string shopeeSecretKeyMobile = 17;
    string vnpayTerminalId = 18;
    string vnpaySecretKey = 19;
    string vnpayMerchantCode = 20;
    string vnpayMerchantName = 21;
    string vnpayMerchantType = 22;
    string vnpayRefundSecretKey = 23;
    string vnpayAppId = 24;
    string redirectUrl = 25;  
    repeated string updateOrderUrls = 26;
    repeated string updateTokenUrls = 27;
    repeated string updateApprovalCodeUrls = 28;
    string callbackLoginToken = 29;
    string description = 30;
    bool isActiveOrderGroupId = 31;
    bool isActive = 32;
    string momoIpnUrl = 33;
    string shortCompanyName = 34;
    bool passUserInfoToCreditCard = 35;
}

message GetOnePaymentProviderDataInput {
    string projectId = 1;
    int32 id = 2;
}

message SinglePaymentProviderResult {
    bool success = 1;
    string message = 2;
    PaymentProvider payload = 3;
}

message MultiplePaymentProviderResult {
    bool success = 1;
    string message = 2;
    repeated PaymentProvider payload = 3;
}