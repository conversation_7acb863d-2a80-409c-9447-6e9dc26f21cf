syntax = "proto3";
package commons;

message Id {
    int32 id = 1;
}

message TransactionId {
    string transactionId = 1;
}

message TransactionFilterParams {
    string projectId = 1;
    string orderId = 2;
    string transactionId = 3;
}


message TokenFilterParams {
    string projectId = 1;
    string appUserId = 2;
    string tokenId = 3;
}

message Query {
    string projectId = 1;
    int32 take = 2;
    int32 skip = 3;
    string where = 4;
    string orderBy = 5;
    string appUserId = 6;
}

message QueryCommon {
    string projectId = 1;
    int32 take = 2;
    int32 skip = 3;
    string where = 4;
    map<string, string> orderBy = 5;
    map<string, bool> select = 6;
    string appUserId = 7;
}

message TransactionPagination {
    int32 itemsPerPage = 1;
    int32 currentPage = 2;
    int32 previousPage = 3;
    int32 nextPage = 4;
    int32 lastPage = 5;
    int32 total = 6;
    repeated Transaction items = 7;
}

message TokenPagination {
    int32 itemsPerPage = 1;
    int32 currentPage = 2;
    int32 previousPage = 3;
    int32 nextPage = 4;
    int32 lastPage = 5;
    int32 total = 6;
    repeated VnpayToken items = 7;
}

message Transaction {
    string projectId = 1;
    string paymentMethod = 2;
    string payload = 3;
    double amount = 4;
    string orderInfo = 5;
    string lang = 6;
    string orderId = 7;
    int32 resultCode = 8;
    string message = 9;
    string payUrl = 10;
    string deeplink = 11;
    string qrCodeUrl = 12;
    string qrCode = 13;
    string status = 14;
    string transactionId = 15;
    string storeId = 16;
    string zpTransToken = 17;
    string currency = 18;
    string transactionType = 19;
    string errorMessage = 20;
    string platformType = 21;
    string storeCode = 22;
    string merchantExtId = 23;
    string appUserId = 24;           
    string vnpayCardNumber = 25;
    int32 vnpayStoreToken = 26;
    string vnpayCommand = 27;
    string promotionId = 28;
    string tokenId = 29;
    string firstSixDigitsOfCard = 30;
    string lastFourDigitsOfCard = 31;
    string vnpayBankCode = 32;
    string vnpayBankName = 33;
    string approvalCode = 34;
    repeated string productSkus = 35;
    string responseBody = 36;
    string createdAt = 37;
    string updatedAt = 38;
}

message VnpayToken {
    string projectId = 1;
    string tokenId = 2;
    string appUserId = 3;
    string vnpayCardNumber = 4;
    string vnpayBankCode = 5;
    string lang = 6;
    int32 resultCode = 7;
    string message = 8;
    string payUrl = 9;
    string status = 10;
    string currency = 11;
    string firstSixDigitsOfCard = 12;
    string lastFourDigitsOfCard  = 13;
    string errorMessage = 14;
    string createdAt = 15;
    string updatedAt = 16;
}

message SingleTransResult {
    bool success = 1;
    string message = 2;
    Transaction payload = 3;
}

message SingleVnpayTokenResult {
    bool success = 1;
    string message = 2;
    VnpayToken payload = 3;
}

message MultipleVnpayTokensResult {
    bool success = 1;
    string message = 2;
    TokenPagination payload = 3;
}

message MultipleTransResult {
    bool success = 1;
    string message = 2;
    TransactionPagination payload = 3;
}

message MultipleTransResultWithoutPagination {
    bool success = 1;
    string message = 2;
    repeated Transaction payload = 3;
}

message RefundTransResult {
    bool success = 1;
    string message = 2;
    string payload = 3;
}

message UserInfo {
    string name = 1;
    string firstName = 2;
    string lastName = 3;
    string phoneNumber = 4;
    string email = 5;
    string city = 6;
    string address = 7;
}

message Item {
    double id = 1;
    string name = 2;
    double price = 3 ;
    string currency = 4;
    double quantity = 5;
    double totalPrice = 6;
  
    string description = 7;
    string category = 8;
    string imageUrl = 9;
    string manufacturer = 10;
    string unit = 11;
    double taxAmount = 12;
}

message SingleTransResultVnpay {
    bool success = 1;
    string message = 2;
    VnpayIpnTransactionSinglePayload payload = 3;
}

message VnpayIpnTransactionSinglePayload {
    string RspCode = 1;
    string Message = 2;
    Transaction data = 3;
}

message SingleTokenResultVnpay {
    bool success = 1;
    string message = 2;
    VnpayIpnSingleTokenPayload payload = 3;
}

message VnpayIpnSingleTokenPayload {
    string RspCode = 1;
    string Message = 2;
    VnpayToken data = 3;
}

message CreatePaymentTransactionInput {
    string projectId = 1;
    string paymentMethod = 2;
    double amount = 3;
    string orderId = 4;
    string orderInfo = 5;

    string storeId = 6;
    string storeCode = 7;
    string storeName = 8;

    string merchantExtId = 9;
    string ipAddress = 10;
    string bankCode = 11;

    string platformType = 12;
    string appUserId = 13;

    string redirectUrl = 14;
    string lang = 15;
    UserInfo userInfo = 16;
    repeated Item items = 17;
    repeated string productSkus = 18;
    string zaloPaymentId = 19;
}

message CreateRefundTransactionInput {
    string projectId = 1;
    string transactionId = 2;
    double amount = 3; 
    string description = 4;
    string storeExtId = 5;
    string merchantExtId = 6;
    string ipAddress = 7;
    string createdBy = 8; 
}

message ExportedTransaction {
    bool success = 1;
    string message = 2;
    string payload = 3;
}