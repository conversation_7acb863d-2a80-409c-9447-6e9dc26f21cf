syntax = "proto3";
package payment;
import "commons.proto";
import "momo.proto";
import "zalopay.proto";
import "shopeepay.proto";
import "vnpay.proto";
import "payment-provider.proto";
import "vnpay-qrcode-offline.proto";
import "../proto-health/proto-health-check.proto";

service PaymentMicroservice {
    rpc createPaymentTransaction(commons.CreatePaymentTransactionInput) returns(commons.SingleTransResult) {} 
    rpc createRefundTransaction(commons.CreateRefundTransactionInput) returns(commons.RefundTransResult) {}

    rpc updateMomoTransaction(momo.UpdateMomoTransactionInput) returns(commons.SingleTransResult) {} 

    rpc updateZaloTransaction(zalopay.UpdateZaloTransactionInput) returns(commons.SingleTransResult) {} 

    rpc updateShopeeTransaction(shopeepay.UpdateShopeeTransactionInput) returns(commons.SingleTransResult) {} 

    rpc createVnPayTokenTransaction(vnpay.CreateVnpayTokenTransactionInput) returns(commons.SingleTransResult) {} 
    rpc createVnPayPayAndCreateTokenTransaction(vnpay.CreateVnpayPayAndCreateTokenTransactionInput) returns(commons.SingleTransResult) {} 
    
    rpc createVnPayToken(vnpay.CreateVnpayTokenInput) returns(commons.SingleVnpayTokenResult) {} 
    rpc removeVnPayToken(vnpay.RemoveVnpayTokenInput) returns(commons.SingleVnpayTokenResult) {} 
    rpc updateVnpayCreateToken(vnpay.UpdateVnpayCreateTokenInput) returns(commons.SingleTokenResultVnpay) {}

    rpc updateVnpayTransaction(vnpay.UpdateVnpayTransactionInput) returns(commons.SingleTransResultVnpay) {} 
    rpc updateVnpayTokenPaymentTransaction(vnpay.UpdateVnpayTokenPaymentTransactionInput) returns(commons.SingleTransResultVnpay) {} 
    rpc updateVnpayQrCodeOfflineTransaction(vnpay_qrcode_offline.UpdateVnpayQrCodeOfflineTransactionInput) returns(vnpay_qrcode_offline.UpdateVnpayQrCodeOfflineTransactionResponse) {}
    
    rpc findOneTransactionByTransactionId(commons.TransactionFilterParams) returns(commons.SingleTransResult) {} 
    rpc findTransactionsByOrderId(commons.TransactionFilterParams) returns(commons.MultipleTransResultWithoutPagination) {} 
    rpc findAllTransactions(commons.Query) returns(commons.MultipleTransResult) {}
    rpc findAllTransactionForExport(commons.QueryCommon) returns(commons.ExportedTransaction) {}

    rpc findOneRefundTransaction(commons.TransactionFilterParams) returns(commons.RefundTransResult) {} 
    rpc findAllRefundTransactions(commons.Query) returns(commons.RefundTransResult) {}

    rpc findOneToken(commons.TokenFilterParams) returns(commons.SingleVnpayTokenResult) {}
    rpc findAllTokens(commons.Query) returns(commons.MultipleVnpayTokensResult) {}

    rpc createPaymentProvider(payment_provider.CreatePaymentProviderDataInput) returns(payment_provider.SinglePaymentProviderResult) {} 
    rpc updatePaymentProvider(payment_provider.PaymentProvider) returns(payment_provider.SinglePaymentProviderResult) {} 
    rpc findOnePaymentProvider(payment_provider.GetOnePaymentProviderDataInput) returns(payment_provider.SinglePaymentProviderResult) {}
    rpc removeOnePaymentProvider(payment_provider.GetOnePaymentProviderDataInput) returns(payment_provider.SinglePaymentProviderResult) {}
    rpc findAllPaymentProviders(commons.Query) returns(payment_provider.MultiplePaymentProviderResult) {}

    rpc Check(proto_health.HealthCheckRequest) returns (proto_health.HealthCheckResponse);
}