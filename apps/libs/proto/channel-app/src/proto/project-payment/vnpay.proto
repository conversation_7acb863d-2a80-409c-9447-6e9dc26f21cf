syntax = "proto3";
package vnpay;
import "commons.proto";

message CreateVnpayTokenTransactionInput {
    string projectId = 1;
    string paymentMethod = 2;
    double amount = 3;
    string orderId = 4;
    string orderInfo = 5;

    string storeId = 6;
    string storeCode = 7;
    string merchantExtId = 8;
    string ipAddress = 9;
    string bankCode = 10;

    string platformType = 11;
    string appUserId = 12;

    string redirectUrl = 13;
    string lang = 14;
    UserInfo userInfo = 15;
    repeated Item items = 16;

    string tokenId = 17;
    string cancelUrl = 18;
    string promotionId = 19;
}

message CreateVnpayPayAndCreateTokenTransactionInput {
    string projectId = 1;
    string paymentMethod = 2;
    double amount = 3;
    string orderId = 4;
    string orderInfo = 5;

    string storeId = 6;
    string storeCode = 7;
    string merchantExtId = 8;
    string ipAddress = 9;
    string bankCode = 10;

    string platformType = 11;
    string appUserId = 12;

    string redirectUrl = 13;
    string lang = 14;
    UserInfo userInfo = 15;
    repeated Item items = 16;

    string cancelUrl = 17;
    int32 vnpayStoreToken = 18;
    string promotionId = 19;
}

message CreateVnpayTokenInput {
    string projectId = 1;
    string appUserId = 2;
    string ipAddress = 3;
    string cancelUrl = 4;
    string redirectUrl = 5;
}

message RemoveVnpayTokenInput {
    string projectId = 1;
    string appUserId = 2;
    string tokenId = 3;
    string ipAddress = 4;
    string description = 5;
}

message UpdateVnpayTransactionInput {
    string vnp_TmnCode = 1;
    double vnp_Amount = 2;
    string vnp_BankCode = 3;
    string vnp_BankTranNo = 4;
    string vnp_CardType = 5;
    double vnp_PayDate = 6;
    string vnp_OrderInfo = 7;
    double vnp_TransactionNo = 8;
    string vnp_ResponseCode = 9;
    string vnp_TransactionStatus = 10;
    string vnp_TxnRef = 11;
    string vnp_SecureHashType = 12;
    string vnp_SecureHash = 13;
}

message UpdateVnpayCreateTokenInput {
    string vnp_app_user_id = 1;
    string vnp_token = 2;
    string vnp_card_number = 3;
    string vnp_command = 4;
    string vnp_tmn_code = 5;
    string vnp_response_code = 6;
    string vnp_txn_ref = 7;
    string vnp_txn_desc = 8;
    double vnp_transaction_no = 9;
    string vnp_card_type = 10;
    string vnp_bank_code = 11;
    string vnp_bank_tran_no = 12;
    string vnp_transaction_status = 13;
    double vnp_pay_date = 14;
    string vnp_secure_hash_type = 15;
    string vnp_secure_hash = 16;
}

message UpdateVnpayTokenPaymentTransactionInput {
    string vnp_app_user_id = 1;
    string vnp_token = 2;
    string vnp_card_number = 3;
    string vnp_command = 4;
    string vnp_card_type = 5;
    string vnp_bank_code = 6;
    string vnp_bank_tran_no = 7;
    string vnp_tmn_code = 8;
    string vnp_txn_ref = 9;
    string vnp_txn_desc = 10;
    string vnp_amount = 11;
    string vnp_curr_code = 12;
    string vnp_response_code = 13;
    string vnp_pay_date = 14;
    string vnp_transaction_no = 15;
    string vnp_transaction_status = 16;
    string vnp_secure_hash_type = 17;
    string vnp_secure_hash = 18;
}