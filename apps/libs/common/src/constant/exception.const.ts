import { status } from '@grpc/grpc-js'
import { HttpStatus } from '@nestjs/common'

export type ExceptionType = keyof typeof ExceptionMessages

export const ExceptionMessages = {
  SUCCESS: {
    message: 'Request processed successfully.',
  },
  '000-400': {
    message: 'Bad Request. The server could not understand the request due to invalid syntax.',
  },
  '000-401': {
    message: 'Unauthorized. The client must authenticate itself to get the requested response',
  },
  ACCESS_DENIED: {
    message: 'Forbidden. The client does not have access rights to the content, or directory listing is denied.',
  },
  '000-404': {
    message: 'Not Found. The server cannot find the requested resource.',
  },
  CNF_CONFLICT_REQUEST: {
    message:
      'Conflict. This response is sent when a request conflicts with the current state of the server. Could be used for duplicate requests.',
  },
  '000-500': {
    message: "Internal Server Error. The server has encountered a situation it doesn't know how to handle.",
    code: status.INTERNAL,
  },

  INVALID_DATA: {
    message: 'Invalid request. One or several input parameters are invalid.',
  },

  FPT_GET_ACCESS_TOKEN_FAILURE: {
    message: 'FPT Get access token failure',
  },
  FPT_SEND_SMS_FAILURE: {
    message: 'FPT send SMS failure',
  },
  LCNF_CUSTOMER_NOT_FOUND_BY_ONBOARDING_ID: {
    message: 'Customer not found by onboarding id',
    code: status.NOT_FOUND,
  },
  LCNF_CUSTOMER_NOT_FOUND_BY_APPLICATION_ID: {
    message: 'Customer not found by application id',
    code: status.NOT_FOUND,
  },
  LCNF_CUSTOMER_ADDRESS_NOT_FOUND_BY_APPLICATION_ID: {
    message: 'Customer address not found by application id',
    code: status.NOT_FOUND,
  },
  LCNF_NATIONAL_ID_NOT_FOUND_BY_CUSTOMER: {
    message: 'National Id Not Found By Customer',
    code: status.NOT_FOUND,
  },
  LCNF_APPLICATION_MUST_BE_PROCESSING: {
    message: 'Application must be processing',
    code: status.NOT_FOUND,
  },
  LCNF_APPLICATION_WAS_CREATED_BY_ONBOARDING_ID_BEFORE: {
    message: 'Application was created by onboarding id before',
    code: status.ALREADY_EXISTS,
  },
  LCNF_APPLICATION_NOT_FOUND: {
    message: 'Application not found',
    code: status.NOT_FOUND,
  },
  LCNF_SUB_APPLICATION_NOT_FOUND: {
    message: 'Sub application not found',
    code: status.NOT_FOUND,
  },
  LCNF_SUB_APP_SPEC_NOT_FOUND: {
    message: 'Sub app specification  not found',
    code: status.NOT_FOUND,
  },
  LCNF_SUB_APPLICATION_MUST_BE_PROCESSING: {
    message: 'Sub application must be processing',
    code: status.FAILED_PRECONDITION,
  },
  LCNF_SUB_APPLICATION_MUST_BE_APPROVED: {
    message: 'Sub application must be approved',
    code: status.FAILED_PRECONDITION,
  },
  LCNF_SUB_APPLICATION_MUST_BE_SIGNING: {
    message: 'Sub application must be signing',
    code: status.FAILED_PRECONDITION,
  },
  LCNF_VPB_REASON_CODE_IS_INVALID: {
    message: 'VPB reason code is invalid',
    code: status.FAILED_PRECONDITION,
  },
  LCNF_SELECTED_TENURE_NOT_VALID: {
    message: 'Selected tenure not valid',
    code: status.FAILED_PRECONDITION,
  },
  LCNF_NO_HANDLE_ANYMORE_DUE_TO_APPLICATION_IS_STILL_IN_PROCESSING: {
    message: 'No handle anymore due to application is still in processing',
    code: status.INTERNAL,
  },
  GRPC_VALIE_NOT_FOUNND: {
    message: 'Value not found',
    code: status.NOT_FOUND,
  },
  GRPC_INTERNAL_EXCEPTION: {
    message: 'GRPC Internal Exception',
    code: status.INTERNAL,
  },
  GRPC_CONNECTION_EXCEPTION: {
    message: 'GRPC Connection Exception',
    code: status.UNAVAILABLE,
  },
  GRPC_MISS_MATCH_VALIDATION_BETWEEN_TWO_SERVICES: {
    message: 'GRPC miss match validation between two services',
    code: status.INTERNAL,
  },
  GRPC_PROMOTION_APPLICATION_STATUS_IS_INVALID: {
    message: 'GRPC promotion application status is invalid',
    code: status.INTERNAL,
  },
  GRPC_PROMOTION_FINAL_DISCOUNT_NOT_MATCH: {
    message: 'GRPC promotion final discount not match',
    code: status.INVALID_ARGUMENT,
  },
  GRPC_PROMOTION_NOT_FOUND: {
    message: 'GRPC promotion not found',
    code: status.NOT_FOUND,
  },
  GRPC_LCNF_PROMOTION_NOT_MATCH: {
    message: 'GRPC promotion not match',
    code: status.INVALID_ARGUMENT,
  },
  GRPC_PROMOTION_MUST_BE_HELD: {
    message: 'GRPC promotion must be held',
    code: status.FAILED_PRECONDITION,
  },
  GRPC_LCNF_PROMOTION_IS_NOT_VALID_LONGER: {
    message: 'GRPC promotion is not valid longer',
    code: status.FAILED_PRECONDITION,
  },
  GRPC_LCNF_TENURE_NOT_MATCH_WITH_PROMOTION: {
    message: 'GRPC tenure not match with promotion',
    code: status.INVALID_ARGUMENT,
  },
  GRPC_PROMOTION_MUST_BE_PROCESSING_FOR_FURTHER_CHECKING: {
    message: 'GRPC promotion must be processing for further checking',
    code: status.FAILED_PRECONDITION,
  },
  GRPC_PROMOTION_ERROR_BY_SYSTEM: {
    message: 'GRPC promotion error by system',
    code: status.INTERNAL,
  },
  GRPC_PROMOTION_APPLICATION_NOT_FOUND: {
    message: 'GRPC promotion application not found',
    code: status.FAILED_PRECONDITION,
  },
  GRPC_SIGN_CONTRACT_DOES_NOT_SUPPORT_CURRENT_STEP_STAGE: {
    message: 'GRPC sign contract does not support current step stage',
    code: status.FAILED_PRECONDITION,
  },
  GRPC_CALL_CALLBACK_FAILED: {
    message: 'GRPC call callback failed',
    code: status.INTERNAL,
  },
  KAFKA_CAN_NOT_CONNECT_TO_VPB_CORE: {
    message: 'Kafka can not connect to VPB core',
    code: status.UNAVAILABLE,
  },
  KAFKA_CAN_NOT_CONNECT_TO_GATEWAY: {
    message: 'Kafka can not connect to gateway',
    code: status.UNAVAILABLE,
  },
  KAFKA_NO_IMAGE_IN_EKYC_CALLBACK: {
    message: 'Kafka no image in eKYC callback',
    code: status.FAILED_PRECONDITION,
  },
  KAFKA_RECEIVED_EMPTY_RESPONSE: {
    message: 'Kafka received empty response',
    code: status.INTERNAL,
  },
  KAFKA_RECEIVED_EMPTY_VPB_CORE_RESPONSE: {
    message: 'Kafka received empty core response',
    code: status.INTERNAL,
  },
  KAFKA_EKYC_RESULT_IS_NOT_SUPPORTED: {
    message: 'Kafka eKYC result is not supported',
    code: status.INTERNAL,
  },
  KAFKA_RECEIVED_UNHANDLED_FINAL_DISBURSEMENT_STATUS: {
    message: 'Kafka received unhandled final disbursement status',
    code: status.INTERNAL,
  },
  LCNF_GEN_SINGING_OTP_LOCKED: {
    message: 'Request signing OTP is locked',
  },
  LCNF_SUB_APP_ORDER_NOT_FOUND: {
    message: 'Sub app order not found',
    code: status.NOT_FOUND,
  },
  LCNF_CALLBACK_TYPE_NOT_FOUND: {
    message: 'Callback type not found',
    code: status.NOT_FOUND,
  },
  LCNF_VPB_STEP_IS_NOT_ONBOARDING: {
    message: 'VPB step is not onboarding',
    code: status.FAILED_PRECONDITION,
  },
  LCNF_VPB_SYSTEM_IS_INTERRUPTED: {
    message: 'VPB system is interrupted',
    code: status.UNAVAILABLE,
  },
  LCNF_TENURE_EXIST: {
    message: 'Tenure already exists',
    code: HttpStatus.CONFLICT,
  },
  LCNF_VALUE_NOT_FOUND: {
    message: 'Value not found',
    code: HttpStatus.NOT_FOUND,
  },
  LCNF_CAN_NOT_UPDATE_THIS_VALUE: {
    message: 'Can not update this value',
    code: HttpStatus.CONFLICT,
  },
  LCNF_ACTIVATED_SKU_ITEM_WITH_SAME_MERCHANTS_ALREADY_EXISTS: {
    message: 'Activated SKU item with same merchants already exists',
    code: HttpStatus.CONFLICT,
  },
  LCNF_USERNAME_ALREADY_EXISTS: {
    message: 'Username already exists',
    code: HttpStatus.CONFLICT,
  },
  HTTP_DATA_NOT_FOUND: {
    message: 'Data not found',
    code: HttpStatus.NOT_FOUND,
  },
  HTTP_CODE_ALREADY_EXISTS: {
    message: 'Code already exists',
    code: HttpStatus.CONFLICT,
  },
  HTTP_DATA_ALREADY_EXISTS: {
    message: 'Data already exists',
    code: HttpStatus.CONFLICT,
  },
  HTTP_PROMOTION_CODE_ALREADY_EXISTS: {
    message: 'Promotion code already exists',
    code: HttpStatus.CONFLICT,
  },
  HTTP_PROMOTION_NOT_FOUND: {
    message: 'Promotion not found',
    code: HttpStatus.NOT_FOUND,
  },
  HTTP_MIN_PAYBACK_TIME_ALREADY_EXISTS: {
    message: 'Min payback time already exists',
    code: HttpStatus.CONFLICT,
  },
  HTTP_CAN_NOT_DISABLE_REQUIRED_RECORD: {
    message: 'Can not disable required record',
    code: HttpStatus.CONFLICT,
  },
  HTTP_TYPE_IS_NOT_SUPPORTED: {
    message: 'Type is not supported',
    code: HttpStatus.CONFLICT,
  },
  HTTP_CREATING_ERROR: {
    message: 'Creating error',
    code: HttpStatus.INTERNAL_SERVER_ERROR,
  },
  LCNF_UNAUTHORIZED: {
    message: 'Unauthorized',
    code: HttpStatus.UNAUTHORIZED,
  },
}
