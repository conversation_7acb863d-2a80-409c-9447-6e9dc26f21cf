export enum EOrderBy {
  DESC = 'desc',
  ASC = 'asc',
}

export enum EOrderByInput {
  CREATED_AT_ASC = 'createdAt:asc',
  CREATED_AT_DESC = 'createdAt:desc',
  CREATED_TIME_ASC = 'createdTime:asc',
  CREATED_TIME_DESC = 'createdTime:desc',
  UPDATED_AT_ASC = 'updatedAt:asc',
  UPDATED_AT_DESC = 'updatedAt:desc',
  ACTIVATED_ASC = 'activated:asc',
  ACTIVATED_DESC = 'activated:desc',
  IS_ACTIVE_ASC = 'isActive:asc',
  IS_ACTIVE_DESC = 'isActive:desc',
  EXPIRED_IN_ASC = 'expiredIn:asc',
  EXPIRED_IN_DESC = 'expiredIn:desc',
  PRIORITY_ASC = 'priority:asc',
  PRIORITY_DESC = 'priority:desc',
  MIN_PAYBACK_TIME_ASC = 'minPaybackTime:asc',
  MIN_PAYBACK_TIME_DESC = 'minPaybackTime:desc',
  USAGE_LIMIT_ASC = 'usageLimit:asc',
  USAGE_LIMIT_DESC = 'usageLimit:desc',
  NAME_ASC = 'name:asc',
  NAME_DESC = 'name:desc',
  PHONE_ASC = 'phone:asc',
  PHONE_DESC = 'phone:desc',
  EMAIL_ASC = 'email:asc',
  EMAIL_DESC = 'email:desc',
}
