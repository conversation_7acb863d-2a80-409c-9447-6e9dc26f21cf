import { EOrderBy, EOrderByInput } from '@common'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsNumber, IsOptional, Max, Min } from 'class-validator'

export class PaginationQueryDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => {
    const parsedValue = parseInt(value, 10)
    return isNaN(parsedValue) || parsedValue < 1 ? 1 : parsedValue // Default to 1 if invalid
  })
  @IsNumber()
  @Min(1)
  page: number = 1

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => {
    const parsedValue = parseInt(value, 10)
    return isNaN(parsedValue) || parsedValue < 1 ? 10 : parsedValue // Default to 10 if invalid
  })
  @IsNumber()
  @Min(1)
  @Max(200)
  pageSize: number = 10

  @ApiPropertyOptional({
    enum: EOrderByInput,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined

    const [field, direction] = value.split(':')
    if (!field || !direction || !Object.values(EOrderBy).includes(direction.toLowerCase())) {
      throw new Error('Invalid orderBy format. Use field:direction (e.g., createdAt:asc).')
    }

    return { [field]: direction.toLowerCase() }
  })
  orderBy?: Record<string, EOrderBy> = { createdAt: EOrderBy.DESC }
}
