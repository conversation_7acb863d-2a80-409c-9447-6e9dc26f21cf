import { CommonModule } from '@common/common.module'
import { GlobalExceptionFilter } from '@common/filters/exception.filter'
import { GrpcLoggingInterceptor } from '@common/interceptors/gprc-logging.interceptor'
import { KafkaLoggingInterceptor } from '@common/interceptors/kafka-logging.interceptor'
import { ScopeVariableMiddleware } from '@common/middlewares'
import { ProviderModule } from '@common/providers/provider.module'
import { MiddlewareConsumer, Module, NestModule, ValidationPipe } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core'
import { GrpcClientModule } from 'apps/libs/grpc-client/src'
import { PrismaModule } from 'apps/libs/prisma/src/prisma.module'
import configuration from './configs/configuration'
import { AuthGuard } from './modules/auth/auth.guard'
import { AuthModule } from './modules/auth/auth.module'
import { BlackListItemsModule } from './modules/black-list-items/black-list-items.module'
import { BlacklistAddressModule } from './modules/blacklist-address/blacklist-address.module'
import { HealthModule } from './modules/health/health.module'
import { InternalModule } from './modules/internal/internal.module'
import { LocationRestrictionModule } from './modules/location-restriction/location-restriction.module'
import { MerchantConfigsModule } from './modules/merchant-configs/merchant-configs.module'
import { OtpConfigModule } from './modules/otp-config/otp-config.module'
import { PlatformBlockingModule } from './modules/platform-blocking/platform-blocking.module'
import { PromotionsModule } from './modules/promotions/promotions.module'
import { ProvincesModule } from './modules/provinces/provinces.module'
import { PurchaseLimitationsModule } from './modules/purchase-limitations/purchase-limitations.module'
import { RejectCodesModule } from './modules/reject_codes/reject_codes.module'
import { RiskManagementModule } from './modules/risk-management/risk-management.module'
import { RuleSegmentsModule } from './modules/rule-segments/rule-segments.module'
import { SearchModule } from './modules/search/search.module'
import { ServicesModule } from './modules/services/services.module'
import { TenorsModule } from './modules/tenors/tenors.module'
import { TransactionAmountModule } from './modules/transaction-amount/transaction-amount.module'
import { UsageLimitMatrixModule } from './modules/usage-limitations/modules/usage-limit-matrix.module'
import { UsersModule } from './modules/users/users.module'
import { WorkflowServicesModule } from './modules/workflow-services/workflow-services.module'
import { WorkflowsModule } from './modules/workflows/workflows.module'
import { UsageLimitGroupsModule } from './modules/usage-limitations/modules/usage-limit-groups.module'
import { CustomerGroupsModule } from './modules/customer-groups/customer-groups.module';
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
    }),
    PrismaModule,
    CommonModule,
    GrpcClientModule,
    ProviderModule,
    InternalModule,
    AuthModule,
    UsersModule,
    TransactionAmountModule,
    TenorsModule,
    WorkflowServicesModule,
    WorkflowsModule,
    BlackListItemsModule,
    PurchaseLimitationsModule,
    HealthModule,
    PromotionsModule,
    BlacklistAddressModule,
    MerchantConfigsModule,
    LocationRestrictionModule,
    SearchModule,
    ServicesModule,
    OtpConfigModule,
    RiskManagementModule,
    RejectCodesModule,
    PlatformBlockingModule,
    RuleSegmentsModule,
    ProvincesModule,
    UsageLimitMatrixModule,
    UsageLimitGroupsModule,
    CustomerGroupsModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_PIPE,
      useFactory: () =>
        new ValidationPipe({
          transform: true,
          whitelist: true,
          validationError: {
            target: false,
            value: false,
          },
          stopAtFirstError: true,
        }),
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: KafkaLoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: GrpcLoggingInterceptor,
    },

    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
  ],
})
export class UndModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ScopeVariableMiddleware).forRoutes('*') // Apply the middleware to all routes
  }
}
