import { EGender } from '@libs/prisma/client-und'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsBoolean, IsEnum, IsNumber, IsOptional } from 'class-validator'

export class UpdateRuleSegmentConditionDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  minAge: number

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  maxAge: number

  @ApiPropertyOptional({ enum: EGender })
  @IsOptional()
  @IsEnum(EGender)
  gender: EGender

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  minIncome: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isLocationCheckEnabled: boolean

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isSourceCheckEnabled: boolean

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isEligibleListEnabled: boolean
}
