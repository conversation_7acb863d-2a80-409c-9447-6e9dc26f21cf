import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { ArrayMinSize, IsNumber, ValidateNested } from 'class-validator'

class EligibleListDto {
  @ApiProperty()
  @Type(() => Number)
  @IsNumber()
  eligibleId: string
}

export class UpsertEligibleListForConditionDto {
  @ApiProperty({ type: [EligibleListDto] })
  @ArrayMinSize(0)
  @ValidateNested({ each: true })
  @Type(() => EligibleListDto)
  list: EligibleListDto[]
}
