import { EAcquisitionSource, EInstallChannel, EOrderBy, EPlatformKey } from '@common'
import { ExceptionMessages, platformBlockingMessageMapping } from '@common/constant'
import { EServiceCode, EWorkflowCode } from '@common/enums/und.enum'
import { HttpBusinessException } from '@common/exceptions'
import { AppStorageService } from '@common/storage'
import { EReferenceType, ESourceType, Prisma, RuleSegmentService } from '@libs/prisma/client-und'
import { Injectable } from '@nestjs/common'
import { RuleSegmentConditionRepository } from 'apps/libs/prisma/src/und/repositories/rule-segment-condition.repository'
import { RuleSegmentServiceRepository } from 'apps/libs/prisma/src/und/repositories/rule-segment-service.repository'
import { RuleSegmentRepository } from 'apps/libs/prisma/src/und/repositories/rule-segment.repository'
import { WorkflowServiceRepository } from 'apps/libs/prisma/src/und/repositories/workflow-service.repository'
import { UnitOfWorkUnd } from 'apps/libs/prisma/src/und/unit-of-work'

import { ReferenceMappingRepository } from '@libs/prisma/src/und/repositories/reference-mappings.repository'
import { UsageLimitGroupRepository } from '@libs/prisma/src/und/repositories/usage-limit-group.repository'
import { CreateRuleSegmentDto } from '../dto/create-rule-segment.dto'
import { GetRuleSegmentDto } from '../dto/get-rule-segment.dto'
import { SyncRuleSegmentLocationDto } from '../dto/sync-rule-segment-location.dto'
import { UpdateRuleSegmentConditionDto } from '../dto/update-rule-segment-condition.dto'
import { UpdateRuleSegmentServiceDto } from '../dto/update-rule-segment-service.dto'
import { UpdateRuleSegmentDto } from '../dto/update-rule-segment.dto'
import { TFindOneRuleSegmentServiceOutPut } from '../types/rule-segment-service.type'

@Injectable()
export class RuleSegmentsService {
  constructor(
    private readonly unitOfWork: UnitOfWorkUnd,
    private readonly appStorageService: AppStorageService,
    private readonly ruleSegmentRepository: RuleSegmentRepository,
    private readonly ruleSegmentConditionRepository: RuleSegmentConditionRepository,
    private readonly ruleSegmentServiceRepository: RuleSegmentServiceRepository,
    private readonly workflowServiceRepository: WorkflowServiceRepository,
    private readonly usageLimitGroupRepository: UsageLimitGroupRepository,
    private readonly referenceMappingRepository: ReferenceMappingRepository,
  ) {}

  async getServices() {
    const services = await this.workflowServiceRepository.findMany({
      where: { workflowCode: EWorkflowCode.TRANSACTION, isConfigurableByOtherRule: true },
      orderBy: {
        priority: EOrderBy.ASC,
      },
    })

    return services
  }

  async initialDefaultPlatformBlockings(tx: Prisma.TransactionClient) {
    const enumSourceMapping = {
      [EPlatformKey.LCF]: EAcquisitionSource,
      [EPlatformKey.AIR_BRIDGE]: EInstallChannel,
    }

    const platformBlockings = Object.values(EPlatformKey).map((platformKey, index) => {
      return {
        platformKey: platformKey,
        platformName: platformBlockingMessageMapping[platformKey],
        isBlockingUnconfiguredSources: true,
        isBlockingUnknownSources: true,
        isBlockingEnabled: false,
        displayOrder: index + 1,
        createdBy: this.appStorageService.getUser()?.username,
        serviceMapping: EServiceCode.CHECK_RULE_SEGMENT,
      }
    })

    const createdPlatformBlockings = await tx.platformBlocking.createManyAndReturn({
      data: platformBlockings,
    })

    const sources = Object.values(EPlatformKey).flatMap((platformKey, index) => {
      return Object.values(enumSourceMapping[platformKey] ?? {}).map((sourceKey, index) => {
        return {
          platformBlockingId: createdPlatformBlockings.find(item => item.platformKey === platformKey)?.id ?? 0,
          sourceKey: sourceKey,
          sourceName: platformBlockingMessageMapping[sourceKey],
          isBlocked: false,
          displayOrder: index + 1,
          createdBy: this.appStorageService.getUser()?.username,
        }
      })
    })

    await tx.platformBlockingSource.createMany({
      data: sources,
    })

    return createdPlatformBlockings
  }

  async createDefaultUsageLimitGroup({ tx, ruleSegmentId }: { tx: Prisma.TransactionClient; ruleSegmentId: number }) {
    const usageLimitService = await tx.ruleSegmentService.findFirst({
      where: { ruleSegmentId, serviceCode: EServiceCode.CHECK_USAGE_LIMIT },
    })

    if (!usageLimitService) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_CREATING_ERROR)
    }

    const defaultUsageLimitGroup = await tx.usageLimitGroup.findFirst({
      where: { isDefault: true },
    })

    if (!defaultUsageLimitGroup) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_CREATING_ERROR)
    }

    // Default usage limit group for new rule segment
    await tx.referenceMapping.create({
      data: {
        sourceType: ESourceType.RULE_SEGMENT_SERVICE,
        sourceId: usageLimitService.id,
        referenceType: EReferenceType.USAGE_LIMIT_GROUP,
        referenceId: defaultUsageLimitGroup.id,
        isEnabled: true,
        updatedBy: this.appStorageService.getUser()?.username ?? '',
      },
    })
  }

  async create(payload: CreateRuleSegmentDto) {
    const { code, name, description, priority, isActive } = payload

    const existed = await this.ruleSegmentRepository.findFirst({
      where: { code },
    })

    if (existed) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_CODE_ALREADY_EXISTS)
    }

    const services = await this.getServices()

    const data = await this.unitOfWork.transaction(async tx => {
      const created = await tx.ruleSegment.create({
        data: {
          code,
          name,
          description,
          priority,
          isActive,
          createdBy: this.appStorageService.getUser()?.username,
          services: {
            create: services.map(service => ({
              serviceCode: service.serviceCode,
              updatedBy: this.appStorageService.getUser()?.username ?? '',
              isEnabled: service.serviceCode === EServiceCode.CHECK_USAGE_LIMIT ? true : false,
            })),
          },
        },
      })

      // Decrease priority for other rule segments
      await tx.ruleSegment.updateMany({
        where: {
          id: { not: created.id },
          priority: { gte: priority },
        },
        data: {
          priority: { increment: 1 },
          updatedBy: this.appStorageService.getUser()?.username,
        },
      })

      const platformBlockings = await this.initialDefaultPlatformBlockings(tx)

      await tx.ruleSegmentCondition.create({
        data: {
          ruleSegmentId: created.id,
          isLocationCheckEnabled: false,
          isSourceCheckEnabled: false,
          isEligibleListEnabled: false,
          updatedBy: this.appStorageService.getUser()?.username ?? '',
          platformBlockings: {
            create: platformBlockings.map(platformBlocking => ({
              ruleSegmentId: created.id,
              platformBlockingId: platformBlocking.id,
              updatedBy: this.appStorageService.getUser()?.username ?? '',
            })),
          },
        },
      })

      await this.createDefaultUsageLimitGroup({ tx, ruleSegmentId: created.id })

      return created
    })

    return data
  }

  async find(query: GetRuleSegmentDto) {
    const { id, code, name, isActive, page, pageSize, orderBy } = query

    let filter: Prisma.RuleSegmentWhereInput = {
      id,
      code,
      name: { contains: name, mode: Prisma.QueryMode.insensitive },
    }

    if (isActive !== undefined) filter = { ...filter, isActive }

    const [data, total] = await Promise.all([
      this.ruleSegmentRepository.findMany({
        where: { ...filter },
        take: pageSize,
        skip: (page - 1) * pageSize,
        orderBy,
      }),
      this.ruleSegmentRepository.count({
        where: { ...filter },
      }),
    ])

    return {
      page,
      pageSize,
      total,
      data,
    }
  }

  async findOne(id: number) {
    const data = await this.ruleSegmentRepository.findFirst({
      where: { id },
      include: {
        conditions: {
          include: {
            platformBlockings: {
              include: {
                platformBlocking: {
                  include: {
                    sources: true,
                  },
                },
              },
            },
            locations: {
              include: {
                province: true,
              },
            },
          },
        },
        services: true,
      },
    })

    if (!data) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)
    }

    const services = await this.getRuleServiceDetails({
      ruleSegmentId: data.id,
      ruleSegmentServices: data.services,
    })

    return {
      ...data,
      services,
      conditions: data.conditions.map(condition => ({
        ...condition,
        platformBlockings: condition.platformBlockings.map(item => item.platformBlocking),
        locations: condition.locations.map(item => {
          return {
            id: item.id,
            provinceCode: item.provinceCode,
            provinceName: item.province.name,
          }
        }),
      })),
    }
  }

  getRuleServiceDetails = async ({
    ruleSegmentId,
    ruleSegmentServices,
  }: {
    ruleSegmentId: number
    ruleSegmentServices: RuleSegmentService[]
  }) => {
    const services = await this.workflowServiceRepository.findMany({
      where: { workflowCode: EWorkflowCode.TRANSACTION, isConfigurableByOtherRule: true },
      include: {
        service: true,
      },
      orderBy: {
        priority: EOrderBy.ASC,
      },
    })

    const servicesWithUsageLimitGroups = await this.mapUsageLimitGroupsToRuleSegmentService(ruleSegmentServices)

    return services.map(service => {
      let ruleSegmentService = servicesWithUsageLimitGroups.find(item => item.serviceCode === service.serviceCode) ?? {
        ruleSegmentId: ruleSegmentId,
        serviceCode: service.serviceCode,
        isEnabled: false,
        references: [],
      }
      const serviceName = service?.service?.name

      return {
        ...ruleSegmentService,
        serviceName,
      }
    })
  }

  async mapUsageLimitGroupsToRuleSegmentService(ruleSegmentServices: RuleSegmentService[]) {
    const referenceMappings = await this.referenceMappingRepository.findMany({
      where: {
        sourceType: ESourceType.RULE_SEGMENT_SERVICE,
        sourceId: { in: ruleSegmentServices.map(item => item.id) },
        referenceType: EReferenceType.USAGE_LIMIT_GROUP,
        isEnabled: true,
      },
    })

    return ruleSegmentServices.map(service => {
      if (service.serviceCode != EServiceCode.CHECK_USAGE_LIMIT) return { ...service }

      const enabledReference = referenceMappings.find(item => item.sourceId === service.id)

      return {
        ...service,
        enabledReference,
      }
    })
  }

  async update(id: number, payload: UpdateRuleSegmentDto) {
    const { name, description, priority, isActive } = payload

    const existed = await this.ruleSegmentRepository.findFirst({
      where: { id },
    })

    if (!existed) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)
    }

    const data = await this.unitOfWork.transaction(async tx => {
      const updated = await tx.ruleSegment.update({
        where: { id },
        data: {
          name,
          description,
          priority,
          isActive,
          updatedBy: this.appStorageService.getUser()?.username,
        },
      })

      if (priority && priority !== existed.priority) {
        await tx.ruleSegment.updateMany({
          where: {
            id: { not: id },
            priority: { gte: priority },
          },
          data: {
            priority: { increment: 1 },
            updatedBy: this.appStorageService.getUser()?.username,
          },
        })
      }

      return updated
    })

    return data
  }

  async upsertService(
    { ruleSegmentId, serviceCode }: { ruleSegmentId: number; serviceCode: string },
    payload: UpdateRuleSegmentServiceDto,
  ) {
    const { isEnabled } = payload

    const data = await this.ruleSegmentServiceRepository.findFirst({
      where: { ruleSegmentId, serviceCode },
    })

    if (!data) {
      const created = await this.ruleSegmentServiceRepository.create({
        data: {
          ruleSegmentId,
          serviceCode,
          isEnabled,
          updatedBy: this.appStorageService.getUser()?.username ?? '',
        },
      })
      return created
    } else {
      const updated = await this.ruleSegmentServiceRepository.update({
        where: { id: data.id },
        data: {
          isEnabled,
          updatedBy: this.appStorageService.getUser()?.username ?? '',
        },
      })
      return updated
    }
  }

  async updateCondition(
    { ruleSegmentId, conditionId }: { ruleSegmentId: number; conditionId: number },
    payload: UpdateRuleSegmentConditionDto,
  ) {
    const { minAge, maxAge, gender, minIncome, isLocationCheckEnabled, isSourceCheckEnabled, isEligibleListEnabled } =
      payload

    const data = await this.ruleSegmentConditionRepository.update({
      where: { id: conditionId, ruleSegmentId },
      data: {
        minAge,
        maxAge,
        gender,
        minIncome,
        isLocationCheckEnabled,
        isSourceCheckEnabled,
        isEligibleListEnabled,
        updatedBy: this.appStorageService.getUser()?.username ?? '',
      },
    })

    if (!data) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)
    }

    return data
  }

  async syncLocationsForCondition(
    { ruleSegmentId, conditionId }: { ruleSegmentId: number; conditionId: number },
    payload: SyncRuleSegmentLocationDto,
  ) {
    const { list } = payload

    const condition = await this.ruleSegmentConditionRepository.findFirst({
      where: { id: conditionId, ruleSegmentId },
    })

    if (!condition) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)
    }

    const data = await this.unitOfWork.transaction(async tx => {
      await tx.ruleSegmentLocation.deleteMany({
        where: { ruleSegmentConditionId: conditionId },
      })

      const created = await tx.ruleSegmentLocation.createManyAndReturn({
        data: list.map(item => ({
          ruleSegmentId,
          ruleSegmentConditionId: conditionId,
          provinceCode: item.provinceCode,
          updatedBy: this.appStorageService.getUser()?.username ?? '',
        })),
      })

      return created
    })

    return data
  }
}
