import { ExceptionMessages } from '@common/constant'
import { HttpBusinessException } from '@common/exceptions'
import { AppStorageService } from '@common/storage'
import { ReferenceMappingRepository } from '@libs/prisma/src/und/repositories/reference-mappings.repository'
import { UpsertRuleSegmentServiceReferenceDto } from '../dto/upsert-rule-segment-service-reference.dto'
import { RuleSegmentServiceReferencesValidationService } from '../validations/rule-segment-service-references.validation'
import { Injectable } from '@nestjs/common'
import { EReferenceType, ESourceType } from '@libs/prisma/client-und'

@Injectable()
export class RuleSegmentServicesReferencesService {
  constructor(
    private readonly appStorageService: AppStorageService,
    private readonly referenceMappingRepository: ReferenceMappingRepository,
    private readonly ruleSegmentServiceReferencesValidation: RuleSegmentServiceReferencesValidationService,
  ) {}

  async upsert(payload: UpsertRuleSegmentServiceReferenceDto) {
    const { referenceType, referenceId, ruleSegmentServiceId, isEnabled } = payload

    switch (referenceType) {
      case EReferenceType.USAGE_LIMIT_GROUP:
        return await this.handleUsageLimitGroupReferenceChange({
          ruleSegmentServiceId,
          referenceId,
          isEnabled,
          referenceType,
        })
      default:
        throw new HttpBusinessException(ExceptionMessages.HTTP_TYPE_IS_NOT_SUPPORTED)
    }
  }

  async handleUsageLimitGroupReferenceChange({
    referenceType,
    ruleSegmentServiceId,
    referenceId,
    isEnabled,
  }: {
    referenceType: EReferenceType
    ruleSegmentServiceId: number
    referenceId: number
    isEnabled: boolean
  }) {
    const isValidReference = await this.ruleSegmentServiceReferencesValidation.isValidUsageLimitGroupReference({
      usageLimitGroupId: referenceId,
      ruleSegmentServiceId,
    })

    if (!isValidReference) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)
    }

    const data = await this.referenceMappingRepository.upsert({
      where: {
        sourceType_sourceId_referenceType_referenceId: {
          sourceType: ESourceType.RULE_SEGMENT_SERVICE,
          sourceId: ruleSegmentServiceId,
          referenceType,
          referenceId,
        },
      },
      update: {
        isEnabled,
        updatedBy: this.appStorageService.getUser()?.username ?? '',
      },
      create: {
        sourceType: ESourceType.RULE_SEGMENT_SERVICE,
        sourceId: ruleSegmentServiceId,
        referenceType,
        referenceId,
        isEnabled,
        updatedBy: this.appStorageService.getUser()?.username ?? '',
      },
    })

    // Only 1 reference is enabled at a time
    if (isEnabled) {
      await this.referenceMappingRepository.updateMany({
        where: {
          sourceType: ESourceType.RULE_SEGMENT_SERVICE,
          sourceId: ruleSegmentServiceId,
          referenceType,
          referenceId: { not: referenceId },
        },
        data: {
          isEnabled: false,
          updatedBy: this.appStorageService.getUser()?.username ?? '',
        },
      })
    }

    return data
  }
}
