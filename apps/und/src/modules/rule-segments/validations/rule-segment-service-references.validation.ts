import { EServiceCode } from '@common/enums/und.enum'
import { RuleSegmentServiceRepository } from '@libs/prisma/src/und/repositories/rule-segment-service.repository'
import { UsageLimitGroupRepository } from '@libs/prisma/src/und/repositories/usage-limit-group.repository'
import { Injectable } from '@nestjs/common'

@Injectable()
export class RuleSegmentServiceReferencesValidationService {
  constructor(
    private readonly usageLimitGroupRepository: UsageLimitGroupRepository,
    private readonly ruleSegmentServiceRepository: RuleSegmentServiceRepository,
  ) {}

  async isValidUsageLimitGroupReference({ ruleSegmentServiceId, usageLimitGroupId }) {
    const ruleSegmentService = await this.ruleSegmentServiceRepository.count({
      where: { id: ruleSegmentServiceId, serviceCode: EServiceCode.CHECK_USAGE_LIMIT },
    })

    if (!ruleSegmentService) return false

    return await this.usageLimitGroupRepository.count({
      where: { id: usageLimitGroupId },
    })
  }
}
