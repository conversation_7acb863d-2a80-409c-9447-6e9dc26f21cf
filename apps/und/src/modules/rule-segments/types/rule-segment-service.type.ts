import { ReferenceMapping, RuleSegmentService, UsageLimitGroup } from '@libs/prisma/client-und'

export type TFindOneRuleSegmentServiceOutPut = Pick<
  RuleSegmentService,
  'ruleSegmentId' | 'serviceCode' | 'isEnabled'
> & {
  id?: number
  references: ReferenceMapping[]
  serviceName?: string | null
  usageLimitGroups?: (UsageLimitGroup & { isEnabled: boolean })[]
}

// export type TFindOneRuleSegmentServiceOutPutWithReferences = RuleSegmentService & {
//   references: RuleSegmentServiceReference[]
// }
