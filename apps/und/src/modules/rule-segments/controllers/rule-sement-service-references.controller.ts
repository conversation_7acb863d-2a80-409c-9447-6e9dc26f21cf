import { Body, Controller, Put } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger/dist'
import { RuleSegmentServicesReferencesService } from '../services/rule-sement-service-references.service'
import { UpsertRuleSegmentServiceReferenceDto } from '../dto/upsert-rule-segment-service-reference.dto'

@ApiBearerAuth()
@ApiTags('Rule Segments')
@Controller('v1/rule-segment-service-references')
export class RuleSegmentServicesReferencesController {
  constructor(private readonly ruleSegmentServicesReferencesService: RuleSegmentServicesReferencesService) {}

  @Put()
  upsert(@Body() payload: UpsertRuleSegmentServiceReferenceDto) {
    return this.ruleSegmentServicesReferencesService.upsert(payload)
  }
}
