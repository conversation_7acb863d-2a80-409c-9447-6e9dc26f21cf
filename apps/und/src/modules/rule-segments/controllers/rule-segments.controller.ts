import { Body, Controller, Get, Param, Patch, Post, Put, Query } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger/dist'
import { CreateRuleSegmentDto } from '../dto/create-rule-segment.dto'
import { GetRuleSegmentDto } from '../dto/get-rule-segment.dto'
import { SyncRuleSegmentLocationDto } from '../dto/sync-rule-segment-location.dto'
import { UpdateRuleSegmentConditionDto } from '../dto/update-rule-segment-condition.dto'
import { UpdateRuleSegmentServiceDto } from '../dto/update-rule-segment-service.dto'
import { UpdateRuleSegmentDto } from '../dto/update-rule-segment.dto'
import { RuleSegmentsService } from '../services/rule-segments.service'

@ApiBearerAuth()
@ApiTags('Rule Segments')
@Controller('v1/rule-segments')
export class RuleSegmentsController {
  constructor(private readonly ruleSegmentsService: RuleSegmentsService) {}

  @Post()
  create(@Body() payload: CreateRuleSegmentDto) {
    return this.ruleSegmentsService.create(payload)
  }

  @Get()
  find(@Query() query: GetRuleSegmentDto) {
    return this.ruleSegmentsService.find(query)
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.ruleSegmentsService.findOne(+id)
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() payload: UpdateRuleSegmentDto) {
    return this.ruleSegmentsService.update(+id, payload)
  }

  @Put(':ruleSegmentId/services/:serviceCode')
  upsertService(
    @Param('ruleSegmentId') ruleSegmentId: string,
    @Param('serviceCode') serviceCode: string,
    @Body() payload: UpdateRuleSegmentServiceDto,
  ) {
    return this.ruleSegmentsService.upsertService({ ruleSegmentId: +ruleSegmentId, serviceCode }, payload)
  }

  @Patch(':ruleSegmentId/conditions/:conditionId')
  updateCondition(
    @Param('ruleSegmentId') ruleSegmentId: string,
    @Param('conditionId') conditionId: string,
    @Body() payload: UpdateRuleSegmentConditionDto,
  ) {
    return this.ruleSegmentsService.updateCondition(
      { ruleSegmentId: +ruleSegmentId, conditionId: +conditionId },
      payload,
    )
  }

  // @Put(':ruleSegmentId/conditions/:conditionId/eligibleList')
  // upsertEligibleListForCondition(
  //   @Param('ruleSegmentId') ruleSegmentId: string,
  //   @Param('conditionId') conditionId: string,
  //   @Body() payload: UpsertEligibleListForConditionDto,
  // ) {
  //   return this.ruleSegmentsService.upsertEligibleListForCondition(
  //     { ruleSegmentId: +ruleSegmentId, conditionId: +conditionId },
  //     payload,
  //   )
  // }

  @Put(':ruleSegmentId/conditions/:conditionId/locations')
  syncLocationsForCondition(
    @Param('ruleSegmentId') ruleSegmentId: string,
    @Param('conditionId') conditionId: string,
    @Body() payload: SyncRuleSegmentLocationDto,
  ) {
    return this.ruleSegmentsService.syncLocationsForCondition(
      { ruleSegmentId: +ruleSegmentId, conditionId: +conditionId },
      payload,
    )
  }
}
