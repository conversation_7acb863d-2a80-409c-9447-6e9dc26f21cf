import { Injectable } from '@nestjs/common'

import { UsageLimitMatrixRepository } from '@libs/prisma/src/und/repositories/usage-limit-matrix.repository'
import { HttpBusinessException } from '@common/exceptions'
import { ExceptionMessages } from '@common/constant'
import { AppStorageService } from '@common/storage'
import { CreateUsageLimitMatrixDto } from '../dto/usage-limit-matrix/create-usage-limit-matrix.dto'
import { GetUsageLimitMatrixDto } from '../dto/usage-limit-matrix/get-usage-limit-matrix.dto'
import { UpdateUsageLimitMatrixDto } from '../dto/usage-limit-matrix/update-usage-limit-matrix.dto'

@Injectable()
export class UsageLimitMatrixService {
  constructor(
    private readonly usageLimitMatrixRepository: UsageLimitMatrixRepository,
    private readonly appStorageService: AppStorageService,
  ) {}

  async create(dto: CreateUsageLimitMatrixDto) {
    const existed = await this.usageLimitMatrixRepository.findFirst({
      where: { minPaybackTime: dto.minPaybackTime, groupId: dto.groupId },
    })

    if (existed) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_MIN_PAYBACK_TIME_ALREADY_EXISTS)
    }

    return await this.usageLimitMatrixRepository.create({
      data: {
        groupId: dto.groupId,
        name: dto.name,
        description: dto.description,
        minPaybackTime: dto.minPaybackTime,
        usageLimit: dto.usageLimit,
        isActive: dto.isActive,
        createdBy: this.appStorageService.getUser()?.username,
      },
    })
  }

  async findAll(query: GetUsageLimitMatrixDto) {
    const { id, minPaybackTime, usageLimit, isActive, page, pageSize, orderBy, isRequired, groupId } = query
    const filter = { id, minPaybackTime, usageLimit, isActive, isRequired, groupId }
    const [data, total] = await Promise.all([
      this.usageLimitMatrixRepository.findMany({
        where: {
          ...filter,
        },
        take: pageSize,
        skip: (page - 1) * pageSize,
        orderBy,
      }),
      this.usageLimitMatrixRepository.count({
        where: {
          ...filter,
        },
      }),
    ])

    return {
      page,
      pageSize,
      total,
      data,
    }
  }

  async update(id: number, dto: UpdateUsageLimitMatrixDto) {
    const existed = await this.usageLimitMatrixRepository.findFirst({
      where: { id },
    })

    if (!existed) throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)

    if (existed.isRequired && dto.isActive === false) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_CAN_NOT_DISABLE_REQUIRED_RECORD)
    }

    return await this.usageLimitMatrixRepository.update({
      where: { id },
      data: {
        name: dto.name,
        description: dto.description,
        usageLimit: dto.usageLimit,
        isActive: dto.isActive,
        updatedBy: this.appStorageService.getUser()?.username,
      },
    })
  }
}
