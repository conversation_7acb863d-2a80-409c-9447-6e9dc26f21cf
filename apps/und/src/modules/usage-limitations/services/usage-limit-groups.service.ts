import { ExceptionMessages } from '@common/constant'
import { HttpBusinessException } from '@common/exceptions'
import { AppStorageService } from '@common/storage'
import { UsageLimitGroupRepository } from '@libs/prisma/src/und/repositories/usage-limit-group.repository'
import { Injectable } from '@nestjs/common'
import { CreateUsageLimitGroupDto } from '../dto/usage-limit-groups/create-usage-limit-group.dto'
import { UpdateUsageLimitGroupDto } from '../dto/usage-limit-groups/update-usage-limit-group.dto'
import { GetUsageLimitGroupDto } from '../dto/usage-limit-groups/get-usage-limit-group.dto'
import { Prisma } from '@libs/prisma/client-und'

@Injectable()
export class UsageLimitGroupsService {
  constructor(
    private readonly appStorageService: AppStorageService,
    private readonly usageLimitGroupRepository: UsageLimitGroupRepository,
  ) {}

  async create(dto: CreateUsageLimitGroupDto) {
    const { name, description } = dto

    const data = await this.usageLimitGroupRepository.create({
      data: {
        name,
        description,
        createdBy: this.appStorageService.getUser()?.username,
        isDefault: false,
        usageLimitMatrix: {
          create: {
            description: `Default usage limit matrix`,
            name: 'Default',
            minPaybackTime: 0,
            usageLimit: 0,
            isActive: true,
            isRequired: true,
          },
        },
      },
    })

    return data
  }

  async findAll(query: GetUsageLimitGroupDto) {
    const { id, name, isDefault, page, pageSize, orderBy } = query
    let filter: Prisma.UsageLimitGroupWhereInput = { id, isDefault }

    if (name) filter = { ...filter, name: { contains: name, mode: Prisma.QueryMode.insensitive } }

    const [data, total] = await Promise.all([
      this.usageLimitGroupRepository.findMany({
        where: {
          ...filter,
        },
        take: pageSize,
        skip: (page - 1) * pageSize,
        orderBy: [{ isDefault: Prisma.SortOrder.desc }, { ...orderBy }],
      }),
      this.usageLimitGroupRepository.count({
        where: {
          ...filter,
        },
      }),
    ])

    return {
      page,
      pageSize,
      total,
      data,
    }
  }

  findOne(id: number) {
    return this.usageLimitGroupRepository.findFirst({
      where: { id },
      include: {
        usageLimitMatrix: {
          orderBy: {
            minPaybackTime: Prisma.SortOrder.asc,
          },
        },
      },
    })
  }

  async update(id: number, dto: UpdateUsageLimitGroupDto) {
    const { name, description } = dto

    const data = await this.usageLimitGroupRepository.update({
      where: { id },
      data: {
        name,
        description,
        updatedBy: this.appStorageService.getUser()?.username,
      },
    })

    if (!data) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)
    }

    return data
  }
}
