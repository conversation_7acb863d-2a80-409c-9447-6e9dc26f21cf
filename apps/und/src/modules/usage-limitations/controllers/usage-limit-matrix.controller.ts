import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { CreateUsageLimitMatrixDto } from '../dto/usage-limit-matrix/create-usage-limit-matrix.dto'
import { GetUsageLimitMatrixDto } from '../dto/usage-limit-matrix/get-usage-limit-matrix.dto'
import { UpdateUsageLimitMatrixDto } from '../dto/usage-limit-matrix/update-usage-limit-matrix.dto'
import { UsageLimitMatrixService } from '../services/usage-limit-matrix.service'

@ApiTags('Usage limit')
@ApiBearerAuth()
@Controller('v1/usage-limit-matrix')
export class UsageLimitMatrixController {
  constructor(private readonly usageLimitMatrixService: UsageLimitMatrixService) {}

  @Post()
  create(@Body() dto: CreateUsageLimitMatrixDto) {
    return this.usageLimitMatrixService.create(dto)
  }

  @Get()
  findAll(@Query() query: GetUsageLimitMatrixDto) {
    return this.usageLimitMatrixService.findAll(query)
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() dto: UpdateUsageLimitMatrixDto) {
    return this.usageLimitMatrixService.update(+id, dto)
  }
}
