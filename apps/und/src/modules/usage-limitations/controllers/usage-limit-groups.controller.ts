import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { UsageLimitGroupsService } from '../services/usage-limit-groups.service'

import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { CreateUsageLimitGroupDto } from '../dto/usage-limit-groups/create-usage-limit-group.dto'
import { GetUsageLimitGroupDto } from '../dto/usage-limit-groups/get-usage-limit-group.dto'
import { UpdateUsageLimitGroupDto } from '../dto/usage-limit-groups/update-usage-limit-group.dto'

@ApiTags('Usage limit')
@ApiBearerAuth()
@Controller('usage-limit-groups')
export class UsageLimitGroupsController {
  constructor(private readonly usageLimitGroupsService: UsageLimitGroupsService) {}

  @Post()
  create(@Body() dto: CreateUsageLimitGroupDto) {
    return this.usageLimitGroupsService.create(dto)
  }

  @Get()
  findAll(@Query() query: GetUsageLimitGroupDto) {
    return this.usageLimitGroupsService.findAll(query)
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.usageLimitGroupsService.findOne(+id)
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() dto: UpdateUsageLimitGroupDto) {
    return this.usageLimitGroupsService.update(+id, dto)
  }
}
