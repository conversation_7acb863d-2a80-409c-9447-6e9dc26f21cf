import { EOrder<PERSON>y, EOrderByInput } from '@common'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { PaginationQueryDto } from '@und/dto/pagination.dto'
import { Transform, Type } from 'class-transformer'
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator'

export class GetUsageLimitGroupDto extends PaginationQueryDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase() === 'true' || value === true) // Transform 'true' to true and 'false' to false
  @IsBoolean({ message: 'isDefault must be a boolean value' }) // Validate as a boolean
  isDefault?: boolean

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number) // Transform the value to a number
  @IsNumber({}, { message: 'id must be a number' }) // Validate as a number
  id?: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string

  @ApiPropertyOptional({
    enum: [
      EOrderByInput.CREATED_AT_ASC,
      EOrderByInput.CREATED_AT_DESC,
      EOrderByInput.UPDATED_AT_ASC,
      EOrderByInput.UPDATED_AT_DESC,
    ],
  })
  orderBy?: Record<string, EOrderBy> = { createdAt: EOrderBy.DESC }
}
