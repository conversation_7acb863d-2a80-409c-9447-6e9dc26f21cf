import { <PERSON><PERSON><PERSON>rBy, E<PERSON>rderByInput } from '@common'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { PaginationQueryDto } from '@und/dto/pagination.dto'
import { Transform, Type } from 'class-transformer'
import { IsBoolean, IsNumber, IsOptional } from 'class-validator'

export class GetUsageLimitMatrixDto extends PaginationQueryDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase() === 'true' || value === true) // Transform 'true' to true and 'false' to false
  @IsBoolean({ message: 'isActive must be a boolean value' }) // Validate as a boolean
  isActive?: boolean

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase() === 'true' || value === true) // Transform 'true' to true and 'false' to false
  @IsBoolean({ message: 'isRequired must be a boolean value' }) // Validate as a boolean
  isRequired?: boolean

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number) // Transform the value to a number
  @IsNumber({}, { message: 'id must be a number' }) // Validate as a number
  id?: number

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number) // Transform the value to a number
  @IsNumber({}, { message: 'minPaybackTime must be a number' }) // Validate as a number
  minPaybackTime?: number

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number) // Transform the value to a number
  @IsNumber({}, { message: 'usageLimit must be a number' }) // Validate as a number
  usageLimit?: number

  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number) // Transform the value to a number
  @IsNumber({}, { message: 'groupId must be a number' }) // Validate as a number
  groupId?: number

  @ApiPropertyOptional({
    enum: [
      EOrderByInput.CREATED_AT_ASC,
      EOrderByInput.CREATED_AT_DESC,
      EOrderByInput.UPDATED_AT_ASC,
      EOrderByInput.UPDATED_AT_DESC,
      EOrderByInput.MIN_PAYBACK_TIME_ASC,
      EOrderByInput.MIN_PAYBACK_TIME_DESC,
      EOrderByInput.USAGE_LIMIT_ASC,
      EOrderByInput.USAGE_LIMIT_DESC,
    ],
  })
  orderBy?: Record<string, EOrderBy> = { minPaybackTime: EOrderBy.ASC }
}
