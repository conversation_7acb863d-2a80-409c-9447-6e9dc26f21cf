import { ApiPropertyOptional } from '@nestjs/swagger'
import { IsBoolean, IsInt, IsOptional, IsString, Min } from 'class-validator'

export class UpdateUsageLimitMatrixDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsInt()
  @Min(0)
  usageLimit: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isActive: boolean
}
