import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsBoolean, IsInt, IsOptional, IsString, Min } from 'class-validator'

export class CreateUsageLimitMatrixDto {
  @ApiProperty()
  @IsInt()
  groupId: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name: string

  @ApiProperty()
  @IsInt()
  @Min(0)
  minPaybackTime: number

  @ApiProperty()
  @IsInt()
  @Min(0)
  usageLimit: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description: string

  @ApiProperty()
  @IsBoolean()
  isActive: boolean
}
