import { EO<PERSON>r<PERSON><PERSON>, EOrderByInput } from '@common'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { PaginationQueryDto } from '@und/dto/pagination.dto'
import { Transform, Type } from 'class-transformer'
import { IsBoolean, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator'

export class GetCustomerGroupDto extends PaginationQueryDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  id: number

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description: string

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase() === 'true' || value === true)
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean

  @ApiPropertyOptional({
    enum: [
      EOrderByInput.UPDATED_AT_ASC,
      EOrderByInput.UPDATED_AT_DESC,
      EOrderByInput.IS_ACTIVE_ASC,
      EOrderByInput.IS_ACTIVE_DESC,
      EOrderByInput.NAME_ASC,
      EOrderByInput.NAME_DESC,
    ],
  })
  orderBy?: Record<string, EOrderBy> = { name: EOrderBy.ASC }
}
