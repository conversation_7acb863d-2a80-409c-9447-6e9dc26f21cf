import { EOrder<PERSON><PERSON>, EOrderByInput } from '@common'
import { ApiPropertyOptional } from '@nestjs/swagger'
import { PaginationQueryDto } from '@und/dto/pagination.dto'
import { Transform } from 'class-transformer'
import { IsBoolean, IsOptional, IsString } from 'class-validator'

export class GetCustomerGroupDetailDto extends PaginationQueryDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  email: string

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  phone: string

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase() === 'true' || value === true)
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean

  @ApiPropertyOptional({
    enum: [
      EOrderByInput.UPDATED_AT_ASC,
      EOrderByInput.UPDATED_AT_DESC,
      EOrderByInput.IS_ACTIVE_ASC,
      EOrderByInput.IS_ACTIVE_DESC,
      EOrderByInput.PHONE_ASC,
      EOrderByInput.PHONE_DESC,
      EOrderByInput.EMAIL_ASC,
      EOrderByInput.EMAIL_DESC,
    ],
  })
  orderBy?: Record<string, EOrderBy> = { updatedAt: EOrderBy.DESC }
}
