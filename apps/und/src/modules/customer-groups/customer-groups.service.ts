import { ExceptionMessages } from '@common/constant'
import { HttpBusinessException } from '@common/exceptions'
import { AppStorageService } from '@common/storage'
import { Prisma } from '@libs/prisma/client-und'
import { CustomerGroupDetailRepository } from '@libs/prisma/src/und/repositories/customer-group-detail.repository'
import { Injectable } from '@nestjs/common'
import { CustomerGroupRepository } from 'apps/libs/prisma/src/und/repositories/customer-group.repository'
import { CreateCustomerGroupDto } from './dto/create-customer-group.dto'
import { CreateCustomerGroupDetailDto } from './dto/details/create-customer-group-detail.dto'
import { GetCustomerGroupDetailDto } from './dto/details/get-customer-group-detail.dto'
import { UpdateCustomerGroupDetailDto } from './dto/details/update-customer-group-detail.dto'
import { GetCustomerGroupDto } from './dto/get-customer-group.dto'
import { UpdateCustomerGroupDto } from './dto/update-customer-group.dto'

@Injectable()
export class CustomerGroupsService {
  constructor(
    private readonly appStorageService: AppStorageService,
    private readonly customerGroupRepository: CustomerGroupRepository,
    private readonly customerGroupDetailRepository: CustomerGroupDetailRepository,
  ) {}

  async create(dto: CreateCustomerGroupDto) {
    const { name, description, isActive } = dto
    const data = await this.customerGroupRepository.create({
      data: {
        name,
        description,
        isActive,
        updatedBy: this.appStorageService.getUser()?.username ?? '',
      },
    })

    return data
  }

  async findAll(query: GetCustomerGroupDto) {
    const { id, name, description, isActive, orderBy, page, pageSize } = query
    let filter: Prisma.CustomerGroupWhereInput = { id }

    if (name) filter = { ...filter, name: { contains: name, mode: Prisma.QueryMode.insensitive } }
    if (description) filter = { ...filter, description: { contains: description, mode: Prisma.QueryMode.insensitive } }
    if (isActive !== undefined) filter = { ...filter, isActive }

    const [data, total] = await Promise.all([
      this.customerGroupRepository.findMany({
        where: {
          ...filter,
        },
        take: pageSize,
        skip: (page - 1) * pageSize,
        orderBy,
      }),
      this.customerGroupRepository.count({
        where: {
          ...filter,
        },
      }),
    ])

    return {
      page,
      pageSize,
      total,
      data,
    }
  }

  findOne(id: number) {
    const data = this.customerGroupRepository.findFirst({
      where: { id },
    })
    return data
  }

  update(id: number, dto: UpdateCustomerGroupDto) {
    const { name, description, isActive } = dto

    const data = this.customerGroupRepository.update({
      where: { id },
      data: {
        name,
        description,
        isActive,
        updatedBy: this.appStorageService.getUser()?.username ?? '',
      },
    })

    if (!data) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)
    }

    return data
  }

  async updateCustomerGroupDetail(id: number, detailId: number, dto: UpdateCustomerGroupDetailDto) {
    const { phone, email, isActive } = dto

    const data = await this.customerGroupDetailRepository.update({
      where: { id: detailId, customerGroupId: id },
      data: {
        phone,
        email,
        isActive,
        updatedBy: this.appStorageService.getUser()?.username ?? '',
      },
    })

    if (!data) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_NOT_FOUND)
    }

    return data
  }

  async createCustomerGroupDetail(id: number, dto: CreateCustomerGroupDetailDto) {
    const { phone, email, isActive } = dto

    const existing = await this.customerGroupDetailRepository.findFirst({
      where: { customerGroupId: id, phone, email },
    })

    if (existing) {
      throw new HttpBusinessException(ExceptionMessages.HTTP_DATA_ALREADY_EXISTS)
    }

    const data = await this.customerGroupDetailRepository.create({
      data: {
        customerGroupId: id,
        phone,
        email,
        isActive,
        updatedBy: this.appStorageService.getUser()?.username ?? '',
      },
    })

    return data
  }

  async getCustomerGroupDetails(id: number, query: GetCustomerGroupDetailDto) {
    const { email, phone, isActive, orderBy, page, pageSize } = query
    let filter: Prisma.CustomerGroupDetailWhereInput = { customerGroupId: id }

    if (email) filter = { ...filter, email: { contains: email, mode: Prisma.QueryMode.insensitive } }
    if (phone) filter = { ...filter, phone: { contains: phone, mode: Prisma.QueryMode.insensitive } }
    if (isActive !== undefined) filter = { ...filter, isActive }

    const [data, total] = await Promise.all([
      this.customerGroupDetailRepository.findMany({
        where: {
          ...filter,
        },
        take: pageSize,
        skip: (page - 1) * pageSize,
        orderBy,
      }),
      this.customerGroupDetailRepository.count({
        where: {
          ...filter,
        },
      }),
    ])

    return {
      page,
      pageSize,
      total,
      data,
    }
  }
}
