import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { CustomerGroupsService } from './customer-groups.service'
import { CreateCustomerGroupDetailDto } from './dto/details/create-customer-group-detail.dto'
import { CreateCustomerGroupDto } from './dto/create-customer-group.dto'
import { GetCustomerGroupDto } from './dto/get-customer-group.dto'
import { UpdateCustomerGroupDetailDto } from './dto/details/update-customer-group-detail.dto'
import { UpdateCustomerGroupDto } from './dto/update-customer-group.dto'
import { GetCustomerGroupDetailDto } from './dto/details/get-customer-group-detail.dto'

@ApiTags('Customer Groups')
@ApiBearerAuth()
@Controller('v1/customer-groups')
export class CustomerGroupsController {
  constructor(private readonly customerGroupsService: CustomerGroupsService) {}

  @Post()
  create(@Body() dto: CreateCustomerGroupDto) {
    return this.customerGroupsService.create(dto)
  }

  @Get()
  findAll(@Query() query: GetCustomerGroupDto) {
    return this.customerGroupsService.findAll(query)
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.customerGroupsService.findOne(+id)
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() dto: UpdateCustomerGroupDto) {
    return this.customerGroupsService.update(+id, dto)
  }

  @Get(':id/details')
  getCustomerGroupDetails(@Param('id') id: string, @Query() query: GetCustomerGroupDetailDto) {
    return this.customerGroupsService.getCustomerGroupDetails(+id, query)
  }

  @Post(':id/details')
  createCustomerGroupDetail(@Param('id') id: string, @Body() dto: CreateCustomerGroupDetailDto) {
    return this.customerGroupsService.createCustomerGroupDetail(+id, dto)
  }

  @Patch(':id/details/:detailId')
  updateCustomerGroupDetailDto(
    @Param('id') id: string,
    @Param('detailId') detailId: string,
    @Body() dto: UpdateCustomerGroupDetailDto,
  ) {
    return this.customerGroupsService.updateCustomerGroupDetail(+id, +detailId, dto)
  }
}
